import 'package:flutter_triple/flutter_triple.dart';
import 'package:play_to_vibe/app/features/user/domain/entities/user_entity.dart';
import 'package:play_to_vibe/app/features/user/domain/usecases/register_user_usecase.dart';

class AppStore extends Store<UserEntity> {
  final RegisterUserUseCase _registerUserUseCase;

  AppStore(this._registerUserUseCase)
      : super(const UserEntity(name: '', onesignalId: ''));

  registerUser() async {
    setLoading(true);
    //final result = await _registerUserUseCase();
    //result.fold((left) => {setError(left)}, (right) => getTermsHtml(right));
    setLoading(false);
  }
}
