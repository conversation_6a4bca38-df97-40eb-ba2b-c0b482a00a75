import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:play_to_vibe/app/features/start/presenter/widgets/intro/three_circle_widget.dart';
import 'package:play_to_vibe/app/services/remote_config/remote_config_store.dart';
import 'package:play_to_vibe/app/theme/colors.dart';
import 'package:play_to_vibe/app/widgets/button.dart';
import 'package:play_to_vibe/app/widgets/custom_image.dart';
import 'package:play_to_vibe/core/utils/app_env.dart';
import 'package:play_to_vibe/core/utils/assets_strings.dart';
import 'package:play_to_vibe/core/utils/quycky_icons_icons.dart';
import 'package:sizer/sizer.dart';

class IntroCard1 extends StatefulWidget {
  final Function() onNext;
  final Function() onClose;

  const IntroCard1({super.key, required this.onClose, required this.onNext});

  @override
  State<IntroCard1> createState() => _IntroCard1();
}

class _IntroCard1 extends State<IntroCard1> {
  int stateRadio = 0;
  Map<int, dynamic> cardsData = {};
  int lastTimeWhenCardSlide = 0;

  void _fillCardsData() {
    String card3Comment =
        'Build your unique profile and unlock\nthe chance to score real-life prizes.';
    String card3Image = Assets.png.intro3;
    if (Platform.isAndroid) {
      final remoteConfigStore = Modular.get<RemoteConfigStore>();
      if (remoteConfigStore.state.popupDisallowedVersions.isNotEmpty &&
          remoteConfigStore.state.popupDisallowedVersions
              .contains(AppEnv.appVersion)) {
        card3Image = Assets.png.intro3Android;
        card3Comment =
            'Build your unique profile\nand unlock exciting features.';
      }
    }
    cardsData[0] = {
      'title': 'PLAY',
      'image': Container(
        padding: EdgeInsets.only(top: 36, bottom: 2.4.h),
        // padding: EdgeInsets.only(top: 36, bottom: 2.8.h),
        width: 79.w,
        child: CustomImage(
          Assets.png.intro2,
        ),
      ), //Assets.png.intro1,
      'comment':
          'Answer fun, mind-opening questions\nand discover more about yourself and others.',
    };

    cardsData[1] = {
      'title': 'VIBE',
      'image': Container(
        padding: EdgeInsets.only(top: 36, bottom: 2.4.h),
        width: 84.08.w,
        child: CustomImage(
          Assets.png.intro4,
        ),
      ),
      'comment':
          'Our Vibe Check syncs you with players\nbased on your match percentage.',
    };
    cardsData[2] = {
      'title': 'MATCH',
      'image': Container(
        padding: EdgeInsets.only(top: 36, bottom: 2.8.h),
        width: 84.08.w,
        child: CustomImage(
          Assets.png.intro1,
          fit: BoxFit.contain,
        ),
      ),
      'comment':
          'Match up with like-minded players from\naround the world in real time.',
    };
    // cardsData[2] = {
    //   'title': 'DISCOVER',
    //   'image': Container(
    //     padding: EdgeInsets.only(top: 36, bottom: 2.4.h),
    //     width: 79.w,
    //     child: CustomImage(
    //       card3Image,
    //     ),
    //   ),
    //   'comment': card3Comment,
    // };
  }

  @override
  void initState() {
    _fillCardsData();
    super.initState();
  }

  void handleChangeValue(int? value) {
    if (value! >= cardsData.length) {
      widget.onClose();
      return;
    }

    setState(() {
      stateRadio = value;
    });
  }

  void handleNext() {
    handleChangeValue(stateRadio + 1);
  }

  void handleBack() {
    handleChangeValue(stateRadio - 1);
  }

  void handleIntroCardSlide(DragUpdateDetails details) {
    final currentTime = DateTime.now().millisecondsSinceEpoch;
    if ((currentTime - lastTimeWhenCardSlide) < 500) {
      return;
    }
    lastTimeWhenCardSlide = currentTime;
    if (details.delta.dx > 0 && stateRadio > 0) {
      handleBack();

      print('right');
      return;
    }
    if (details.delta.dx < 0 && stateRadio <= 3) {
      handleNext();
      print('left');
    }
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onPanUpdate: handleIntroCardSlide,
      child: Card(
        color: Colors.white,
        shape: const RoundedRectangleBorder(
            borderRadius: BorderRadius.only(
                topLeft: Radius.circular(20),
                topRight: Radius.circular(20),
                bottomLeft: Radius.circular(30),
                bottomRight: Radius.circular(30))),
        child: Padding(
          padding: const EdgeInsets.all(0.0),
          child: Container(
            decoration: const BoxDecoration(
                borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(20),
                    topRight: Radius.circular(20),
                    bottomLeft: Radius.circular(30),
                    bottomRight: Radius.circular(30)),
                color: CustomColors.cultured),
            child: Column(
              children: [
                cardsData[stateRadio]['image'],
                Padding(
                  padding: EdgeInsets.only(bottom: 4),
                  child: Text(
                    cardsData[stateRadio]['title'],
                    textAlign: TextAlign.center,
                    style: const TextStyle(
                        fontFamily: "Roboto",
                        letterSpacing: 1,
                        fontWeight: FontWeight.bold,
                        color: CustomColors.primary,
                        fontSize: 30),
                  ),
                ),
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: 2.3.w),
                  child: Text(
                    cardsData[stateRadio]['comment'],
                    textAlign: TextAlign.center,
                    style: const TextStyle(
                        letterSpacing: 0.2,
                        height: 0,
                        fontFamily: "Roboto",
                        fontWeight: FontWeight.w500,
                        color: CustomColors.primary,
                        fontSize: 15),
                  ),
                ),
                const Spacer(),
                Padding(
                  padding: EdgeInsets.only(right: 15.0, left: 15.0, bottom: 15),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      SizedBox(
                        width: 18.w,
                        height: 3.9.h,
                        child: Button(
                          autoSized: true,
                          onPressed: widget.onClose,
                          text: 'SKIP',
                          fontSize: 15.sp,
                          color: Colors.transparent,
                          textColor: CustomColors.primary,
                        ),
                      ),
                      ThreeCirclesWidget(
                        itemsCount: cardsData.length,
                        onChange: handleChangeValue,
                        value: stateRadio,
                      ),
                      SizedBox(
                        width: 18.w,
                        height: 3.9.h,
                        child: Button(
                            autoSized: true,
                            onPressed: handleNext,
                            noAnimation: true,
                            child: const Icon(QuyckyIcons.arrow_right,
                                size: 16, color: Colors.white)),
                      ),
                    ],
                  ),
                )
              ],
            ),
          ),
        ),
      ),
    );
  }
}
