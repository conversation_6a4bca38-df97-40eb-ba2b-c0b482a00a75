import 'package:flutter/material.dart';
import 'package:play_to_vibe/app/theme/colors.dart';
import 'package:sizer/sizer.dart';

class ThreeCirclesWidget extends StatelessWidget {
  final int itemsCount;
  final int value;
  final void Function(int? value) onChange;

  const ThreeCirclesWidget(
      {super.key,
      required this.value,
      required this.onChange,
      required this.itemsCount});

  List<Widget> getListItems() {
    List<Widget> res = [];
    for (int i = 0; i < itemsCount; i++) {
      res.add(
        SizedBox(
          width: 30,
          child: Radio<int>(
              value: i,
              groupValue: value,
              onChanged: onChange,
              fillColor: WidgetStateProperty.resolveWith<Color>(
                  (Set<WidgetState> states) => CustomColors.primary),
              activeColor: CustomColors.primary),
        ),
      );
    }

    return res;
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 3.9.h,
      width: 32.w,
      decoration: const BoxDecoration(
          color: CustomColors.cultured,
          borderRadius: BorderRadius.all(Radius.circular(20))),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: getListItems(),
      ),
    );
  }
}
