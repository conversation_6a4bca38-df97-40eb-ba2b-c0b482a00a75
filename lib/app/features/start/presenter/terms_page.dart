import 'dart:async';
import 'package:flutter/cupertino.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:http/http.dart' as http;
import 'package:flutter/material.dart';
import 'package:play_to_vibe/app/widgets/app_header.dart';
import 'package:play_to_vibe/core/utils/app_routes.dart';
import 'package:play_to_vibe/app/widgets/button.dart';
import 'package:play_to_vibe/app/theme/colors.dart';
import 'package:play_to_vibe/core/utils/box_decoration.dart';
import 'package:flutter_html/flutter_html.dart';

class TermsPage extends StatefulWidget {
  final String title;

  const TermsPage({super.key, this.title = 'TermsPage'});

  @override
  TermsPageState createState() => TermsPageState();
}

class TermsPageState extends State<TermsPage> {
  String pageHtml = "";
  bool _isReaded = false;
  final ScrollController _scrollController = ScrollController();
  final Key _scrollTermsKey = const ValueKey<String>('_scrollTerms');

  // final Completer<WebViewController> _controller =
  //     Completer<WebViewController>();

  Future<http.Response> getTerms() {
    var res = http.get(Uri.parse(
        'https://staging-api.playfultech.io:2053/terms/terms_ecc990f3ab.html'));
    res.then((value) => setState(() {
          pageHtml = value.body;
        }));
    return res;
  }

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(() {
      if (!_isReaded && _scrollController.position.extentAfter <= 100) {
        setState(() {
          _isReaded = true;
        });
      }
    });
    getTerms();
    // if (Platform.isAndroid) {
    //   // WebView.platform = SurfaceAndroidWebView();
    // }
  }

  Widget getWebView() {
    return Html(
      data: pageHtml,
      style: {"body": Style(fontSize: FontSize.large)},
    );
  }

  void handleAccept() {
    if (_isReaded) {
      Modular.to.pushReplacementNamed(AppRoutes.userRegister());
      return;
    }
    showDialog(
        context: context,
        builder: (_) => CupertinoAlertDialog(
              title: const Text('Alert'),
              content: const Text("Please read until the end to accept"),
              actions: [
                CupertinoDialogAction(
                  onPressed: () => Navigator.of(context).pop(),
                  isDefaultAction: true,
                  child: const Text("Ok"),
                )
              ],
            ));
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        width: double.infinity,
        height: double.infinity,
        decoration: getDefault(),
        child: SafeArea(
          child: Column(
            children: [
              const AppHeader(
                title: "TERMS AND CONDITIONS OF USE",
              ),
              SizedBox(
                  height: MediaQuery.of(context).size.height * 0.7,
                  width: MediaQuery.of(context).size.width * 0.8,
                  child: SingleChildScrollView(
                      controller: _scrollController, child: getWebView())),
              const Spacer(),
              Padding(
                padding: const EdgeInsets.symmetric(vertical: 25.0),
                child: SizedBox(
                  width: double.infinity,
                  child: Opacity(
                    opacity: _isReaded ? 1 : 0.65,
                    child: Button(
                      textColor: CustomColors.orangeSoda,
                      outlined: true,
                      onPressed: handleAccept,
                      text: "Accept",
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
//
// JavascriptChannel _toasterJavascriptChannel(BuildContext context) {
//   return JavascriptChannel(
//       name: 'Toaster',
//       onMessageReceived: (JavascriptMessage message) {
//         ScaffoldMessenger.of(context).showSnackBar(
//           SnackBar(content: Text(message.message)),
//         );
//       });
// }
}
