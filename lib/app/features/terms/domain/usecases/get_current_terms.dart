// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:dartz/dartz.dart';
import 'package:play_to_vibe/app/features/terms/domain/entities/current_terms_entity.dart';
import 'package:play_to_vibe/app/features/terms/domain/repositories/terms_repository.dart';

import 'package:play_to_vibe/core/usecase/errors/failure.dart';
import 'package:play_to_vibe/core/usecase/usecase.dart';

class GetCurrentTermsUseCase implements UseCase<CurrentTermsEntity, NoParams?> {
  final ITermsRepository repository;

  GetCurrentTermsUseCase(
    this.repository,
  );

  @override
  Future<Either<Failure, CurrentTermsEntity>> call(NoParams? params) async {
    return await repository.getCurrentTerms();
  }
}
