import 'package:flutter_modular/flutter_modular.dart';
import 'package:flutter_triple/flutter_triple.dart';
import 'package:flutter/material.dart';
import 'package:play_to_vibe/analytics.dart';
import 'package:play_to_vibe/app/features/terms/domain/entities/current_terms_entity.dart';
import 'package:play_to_vibe/app/features/terms/presenter/controllers/terms_page.dart';
import 'package:play_to_vibe/app/features/terms/presenter/controllers/terms_readed_store.dart';
import 'package:play_to_vibe/app/features/terms/presenter/controllers/terms_store.dart';
import 'package:play_to_vibe/app/theme/colors.dart';
import 'package:play_to_vibe/app/widgets/app_header.dart';
import 'package:play_to_vibe/app/widgets/button.dart';
import 'package:play_to_vibe/app/widgets/gradient_container.dart';
import 'package:play_to_vibe/app/widgets/quycky_modal_progress.dart';
import 'package:play_to_vibe/core/usecase/errors/failure.dart';
import 'package:play_to_vibe/core/utils/app_routes.dart';
import 'package:play_to_vibe/core/utils/assets_strings.dart';
import 'package:play_to_vibe/core/utils/box_decoration.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:play_to_vibe/core/utils/quycky_icons_icons.dart';
import 'package:skeletonizer/skeletonizer.dart';

class TermsPage extends StatefulWidget {
  final String title;

  const TermsPage({super.key, this.title = 'TermsPage'});

  @override
  TermsPageState createState() => TermsPageState();
}

class TermsPageState extends State<TermsPage> {
  final _store = Modular.get<TermsStore>();
  final _storeTermsReaded = Modular.get<TermsReadedStore>();
  final ScrollController _scrollController = ScrollController();
  final Key _scrollTermsKey = const ValueKey<String>('_scrollTerms');

  // final Completer<WebViewController> _controller =
  //     Completer<WebViewController>();

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(() {
      if (!_storeTermsReaded.state &&
          _scrollController.position.extentAfter <= 100) {
        _storeTermsReaded.setReaded(true);
      }
    });
    _store.getCurrentTerms();
    //store.
    // if (Platform.isAndroid) {
    //   // WebView.platform = SurfaceAndroidWebView();
    // }
  }

  void _handleBack() {
    Modular.to.pop();
  }

  Widget getWebView(String pageHtml) {
    return Html(
      data: pageHtml.isEmpty ? TermsHtmlPage.html : pageHtml,
      style: {
        "body": Style(
          color: Colors.white,
          fontFamily: 'Roboto',
          fontSize: FontSize.small,
        )
      },
    );
  }

  Widget getOnErrorWidget(error) {
    return Center(
      child: Text(
        'An error occurred, try again later.',
        style: Theme.of(context)
            .textTheme
            .bodySmall
            ?.copyWith(color: Colors.white),
      ),
    );
  }

  Widget _buildLoadingData() {
    return Column(
      children: List.generate(27, (index) {
        return Padding(
          padding: const EdgeInsets.symmetric(horizontal: 24.0, vertical: 8),
          child: Container(
            width: double.infinity,
            height: (index % 2 == 0) ? 20.0 : 8.0, // Tamanhos diferentes
            color: Colors.grey[300],
          ),
        );
      }),
    );
  }

  Widget getOnStateWidget(String state) {
    return Column(
      children: [
        AppHeader(
            logoSectionLeftWidget: IconButton(
                onPressed: _handleBack,
                icon: const Icon(QuyckyIcons.arrow_left_circle,
                    color: Colors.white)),
            title: "TERMS AND CONDITIONS OF USE",
            logo: LogoType.welcome),
        _store.isLoading
            ? _buildLoadingData()
            : Container(
                margin: const EdgeInsets.only(bottom: 10.0),
                child: SizedBox(
                  height: (MediaQuery.of(context).size.height - 285),
                  width: MediaQuery.of(context).size.width,
                  child: Stack(
                    children: [
                      Row(
                        mainAxisSize: MainAxisSize.max,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Container(
                              height:
                                  (MediaQuery.of(context).size.height - 245),
                              constraints: const BoxConstraints(maxWidth: 340),
                              child: SingleChildScrollView(
                                  controller: _scrollController,
                                  child: getWebView(state))),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
        const Spacer(),
        SizedBox(
          width: double.infinity,
          child: TripleBuilder(
            store: _storeTermsReaded,
            builder: ((context, triple) => Opacity(
                opacity: 1, //_storeTermsReaded.state ? 1 : 0.65,
                child: Button(
                  onPressed: _handleBack,
                  color: Colors.white,
                  textColor: CustomColors.orangeSoda,
                  borderColor: Colors.white,
                  text: "Done",
                ))),
          ),
        ),
        const Spacer(),
      ],
    );
  }

  void handleAccept() async {
    // if (_storeTermsReaded.state) {
    await Analytics.instance.logEvent(name: 'TAC_accept');
    Modular.to.pushReplacementNamed(AppRoutes.tutorial); //userRegister);
    //   return;
    // }
    // showDialog(
    //     context: context,
    //     builder: (_) => CupertinoAlertDialog(
    //           title: const Text('Alert'),
    //           content: const Text("Please read until the end to accept"),
    //           actions: [
    //             CupertinoDialogAction(
    //               onPressed: () => Navigator.of(context).pop(),
    //               isDefaultAction: true,
    //               child: const Text("Ok"),
    //             )
    //           ],
    //         ));
  }

  Widget _buildWithGradientBackground(Widget child) {
    return Container(
        width: double.infinity,
        height: double.infinity,
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFFF93A29),
              // Color(0xFFFE5F7C),
              Color(0xFFFF9B75),
              Color(0xFFFF9B75),
            ],
            stops: [0.0, 0.8, 1.0],
          ),
        ),
        child: child);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        body: _buildWithGradientBackground(
      TripleBuilder(
          store: _store,
          builder: (context, triple) {
            Triple<CurrentTermsEntity> store =
                triple as Triple<CurrentTermsEntity>;
            return Skeletonizer(
                enabled: store.isLoading,
                child: SafeArea(child: getOnStateWidget(store.state.content)));
          }),
    ));
  }
}
