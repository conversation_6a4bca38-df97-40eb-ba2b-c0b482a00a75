import 'package:play_to_vibe/app/features/terms/data/datasources/terms_datasource.dart';
import 'package:play_to_vibe/app/features/terms/domain/entities/current_terms_entity.dart';
import 'package:dartz/dartz.dart';
import 'package:play_to_vibe/app/features/terms/domain/repositories/terms_repository.dart';
import 'package:play_to_vibe/core/usecase/errors/failure.dart';

class TermsRepositoryImplementation implements ITermsRepository {
  final ITermsDatasource datasource;

  TermsRepositoryImplementation(this.datasource);

  @override
  Future<Either<Failure, CurrentTermsEntity>> getCurrentTerms() async {
    try {
      final result = await datasource.getCurrentTerms();
      return Right(result);
    } on Exception {
      return const Left(ServerFailure());
    }
  }

  @override
  Future<Either<Failure, String>> getTermsHtml(String url) async {
    try {
      final result = await datasource.getTermsHtml(url);
      return Right(result);
    } on Exception {
      return const Left(ServerFailure());
    }
  }
}
