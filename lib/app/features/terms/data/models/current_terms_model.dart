import 'dart:convert';

import 'package:play_to_vibe/app/features/terms/domain/entities/current_terms_entity.dart';

class CurrentTermsModel extends CurrentTermsEntity {
  CurrentTermsModel({
    required id,
    required version,
    required content,
    required url,
    required createdAt,
    required updatedAt,
    required pageUrl,
  }) : super(
          id: id,
          version: version,
          content: content,
          url: url,
          createdAt: createdAt,
          updatedAt: updatedAt,
          pageUrl: pageUrl,
        );

  factory CurrentTermsModel.fromRawJson(String str) =>
      CurrentTermsModel.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory CurrentTermsModel.fromJson(Map<String, dynamic> json) =>
      CurrentTermsModel(
        id: json["id"],
        version: json["version"],
        content: json["content"],
        url: json["url"],
        createdAt: DateTime.parse(json["created_at"]),
        updatedAt: DateTime.parse(json["updated_at"]),
        pageUrl: json["pageUrl"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "version": version,
        "content": content,
        "url": url,
        "created_at": createdAt.toIso8601String(),
        "updated_at": updatedAt.toIso8601String(),
        "pageUrl": pageUrl,
      };
}
