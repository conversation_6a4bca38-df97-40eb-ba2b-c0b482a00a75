import 'package:play_to_vibe/app/features/terms/data/datasources/endpoints/terms_endpoints.dart';
import 'package:play_to_vibe/app/features/terms/data/datasources/terms_datasource.dart';
import 'package:play_to_vibe/app/features/terms/data/models/current_terms_model.dart';
import 'package:play_to_vibe/core/http_client/http_client.dart';
import 'package:play_to_vibe/core/usecase/errors/exceptions.dart';

class TermsDatasourceImplementation implements ITermsDatasource {
  final HttpClient client;

  TermsDatasourceImplementation(this.client);

  @override
  Future<CurrentTermsModel> getCurrentTerms() async {
    final response = await client.get(TermsEndpoints.currentTerms(),
        receiveTimeout: const Duration(seconds: 15));
    if (response.statusCode == 200) {
      return CurrentTermsModel.fromJson(response.data);
    }
    throw ServerException();
  }

  @override
  Future<String> getTermsHtml(String url) async {
    final response = await client.get(url);
    if (response.statusCode == 200) {
      return response.data;
    }
    throw ServerException();
  }
}
