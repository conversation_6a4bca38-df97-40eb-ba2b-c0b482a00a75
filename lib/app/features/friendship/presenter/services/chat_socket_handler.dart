import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:play_to_vibe/app/features/friendship/domain/dtos/chat_message_dto.dart';
import 'package:play_to_vibe/app/features/friendship/domain/dtos/erase_history_dto.dart';
import 'package:play_to_vibe/app/features/friendship/domain/dtos/get_messages_between_users_dto.dart';
import 'package:play_to_vibe/app/features/friendship/domain/dtos/get_unread_count_dto.dart';
import 'package:play_to_vibe/app/features/friendship/domain/dtos/mark_as_read_dto.dart';
import 'package:play_to_vibe/app/features/friendship/domain/entities/chat_message_entity.dart';
import 'package:play_to_vibe/app/features/friendship/domain/enumerators/chat_action_enumerator.dart';
import 'package:play_to_vibe/app/features/friendship/presenter/store/chat_store.dart';
import 'package:play_to_vibe/app/features/game/domain/dto/game_message_dto.dart';
import 'package:play_to_vibe/app/features/user/presenter/store/user_store.dart';
import 'package:play_to_vibe/app/services/game_socket/game_socket_service.dart';
import 'package:play_to_vibe/core/enumerators/egame_action.dart';
import 'package:play_to_vibe/core/utils/app_routes.dart';
import 'package:play_to_vibe/core/utils/type_converters.dart';
import 'package:play_to_vibe/core/services/app_lifecycle_service/app_lifecycle_service.dart';
import 'package:play_to_vibe/core/services/notification_service/local_notifications_service.dart';

/// Serviço responsável por gerenciar os handlers globais de chat
/// Deve ser inicializado quando o socket conecta para garantir que
/// o ChatStore seja sempre atualizado independentemente da tela ativa
class ChatSocketHandler {
  final ChatStore _chatStore;
  final UserStore _userStore;
  final GameSocketService _socketService;
  final AppLifecycleService _appLifecycleService = AppLifecycleService();

  bool _isInitialized = false;

  ChatSocketHandler(this._chatStore, this._userStore, this._socketService) {
    // Auto-inicializa quando o socket conectar
    _socketService.addConnectionHandler(_autoInitialize);
  }

  /// Auto-inicializa os handlers quando o socket conecta
  void _autoInitialize() {
    if (!_isInitialized) {
      initialize();
    }
  }

  /// Inicializa os handlers globais de chat
  void initialize() {
    if (_isInitialized) return;

    debugPrint('ChatSocketHandler: Inicializando handlers globais de chat');

    // Adiciona handler global para mensagens de chat
    _socketService.addMessageHandler(
        EGameAction.chat, _handleGlobalChatMessage);

    // Adiciona handler para quando a conexão é estabelecida
    _socketService.addConnectionHandler(_onSocketConnected);

    // Adiciona handler para quando a conexão é perdida
    _socketService.addDisconnectionHandler(_onSocketDisconnected);

    _isInitialized = true;
  }

  /// Método estático para inicializar o ChatSocketHandler globalmente
  static void initializeGlobal() {
    try {
      final handler = Modular.get<ChatSocketHandler>();
      handler.initialize();
      debugPrint('ChatSocketHandler: Inicializado globalmente com sucesso');
    } catch (e) {
      debugPrint('ChatSocketHandler: Erro ao inicializar globalmente - $e');
    }
  }

  /// Remove todos os handlers (usado quando o usuário faz logout)
  void dispose() {
    if (!_isInitialized) return;

    debugPrint('ChatSocketHandler: Removendo handlers globais de chat');

    _socketService.removeMessageHandler(
        EGameAction.chat, _handleGlobalChatMessage);
    _socketService.removeConnectionHandler(_onSocketConnected);
    _socketService.removeDisconnectionHandler(_onSocketDisconnected);

    _isInitialized = false;
  }

  /// Handler global para todas as mensagens de chat
  void _handleGlobalChatMessage(GameMessageDTO gameMessage) {
    try {
      dynamic chatPayload = gameMessage.data;

      if (chatPayload is String) {
        try {
          chatPayload = json.decode(chatPayload);
        } catch (e) {
          debugPrint("ChatSocketHandler: Erro ao decodificar payload: $e");
          return;
        }
      }

      if (chatPayload is Map<String, dynamic>) {
        final action = chatPayload['action'] as String?;
        final specificData = chatPayload['data'];

        if (action != null && specificData != null) {
          _handleChatAction(action, specificData);
        } else {
          debugPrint("ChatSocketHandler: action ou specificData é null");
        }
      } else {
        debugPrint(
            "ChatSocketHandler: Tipo de payload inesperado: ${chatPayload.runtimeType}");
      }
    } catch (e) {
      debugPrint("ChatSocketHandler: Erro ao processar mensagem de chat: $e");
    }
  }

  /// Processa as diferentes ações de chat
  void _handleChatAction(String action, dynamic data) {
    try {
      switch (action) {
        case ChatActionEnumerator.messageReceived:
          _handleMessageReceived(data);
          break;

        case ChatActionEnumerator.messageList:
        case ChatActionEnumerator.messagesBetweenPlayers:
          _handleMessagesList(data);
          break;

        case ChatActionEnumerator.messageSent:
          _handleMessageSent(data);
          break;

        case ChatActionEnumerator.messagesUpdated:
        case ChatActionEnumerator.countAsRead:
          _handleMessagesUpdated(data);
          break;

        case ChatActionEnumerator.unreadCountUpdated:
          _handleUnreadCountUpdated(data);
          break;

        default:
          debugPrint("ChatSocketHandler: Ação não tratada: $action");
      }
    } catch (e) {
      debugPrint("ChatSocketHandler: Erro ao processar ação $action: $e");
    }
  }

  void eraseHistory(String friendIdentifier) {
    if (!_socketService.isConnected) {
      return;
    }

    final params = EraseHistoryDTO(friendIdentifier: friendIdentifier);

    final chatMessageDto = ChatMessageDTO<EraseHistoryDTO>(
      action: ChatActionEnumerator.eraseHistory,
      connectionId: _socketService.connectionId,
      data: params,
    );

    _socketService.sendMessage(
      _socketService.createMessage(
        EGameAction.chat,
        data: chatMessageDto,
      ),
    );
  }

  void fetchMessagesBetweenUsers(String friendIdentifier) {
    final getMessagesData = GetMessagesBetweenUsersDTO(
      otherUserIdentifier: friendIdentifier,
    );

    final chatMessageDto = ChatMessageDTO<GetMessagesBetweenUsersDTO>(
      action: ChatActionEnumerator.getMessagesBetweenUsers,
      connectionId: _socketService.connectionId,
      data: getMessagesData,
    );

    _socketService.sendMessage(
      _socketService.createMessage(
        EGameAction.chat,
        data: chatMessageDto,
      ),
    );
  }

  void fetchUnreadCount() {
    if (!_socketService.isConnected) {
      return;
    }
    final getUnreadCountData = GetUnreadCountDTO(); // Para contagem geral

    final chatMessageDto = ChatMessageDTO<GetUnreadCountDTO>(
      action: ChatActionEnumerator.getUnreadCount,
      connectionId: _socketService.connectionId,
      data: getUnreadCountData,
    );

    _socketService.sendMessage(
      _socketService.createMessage(
        EGameAction.chat,
        data: chatMessageDto,
      ),
    );
  }

  void markMessagesAsRead(List<int> messageIds) {
    if (messageIds.isEmpty) return;

    final validMessageIds = messageIds.where((id) => id > 0).toList();
    if (validMessageIds.isEmpty) return;

    final markAsReadData = MarkAsReadDTO(messageIds: validMessageIds);

    final chatMessageDto = ChatMessageDTO<MarkAsReadDTO>(
      action: ChatActionEnumerator.markAsRead,
      connectionId: _socketService.connectionId,
      data: markAsReadData,
    );

    _socketService.sendMessage(
      _socketService.createMessage(
        EGameAction.chat,
        data: chatMessageDto,
      ),
    );
  }

  /// Processa mensagem recebida
  void _handleMessageReceived(dynamic data) {
    final newMessage = ChatMessageEntity.fromMap(data as Map<String, dynamic>);

    // Adiciona a mensagem ao store
    if ([newMessage.senderIdentifier, newMessage.receiverIdentifier]
        .contains(_chatStore.state.currentConversationFriendIdentifier)) {
      _chatStore.addMessage(newMessage);
      if (Modular.to.path == AppRoutes.friendChat) {
        markMessagesAsRead([newMessage.id]);
        return;
      }
    }

    // Se a mensagem é para o usuário logado e não foi lida, incrementa contador
    if (newMessage.receiverIdentifier == _userStore.state.user.identifier &&
        !newMessage.isRead) {
      _chatStore.incrementUnreadCount(newMessage.senderIdentifier);

      // Mostra notificação se o app estiver em background
      _showNotificationIfAppInBackground(newMessage);
    }

    debugPrint(
        "ChatSocketHandler: Nova mensagem recebida de ${newMessage.senderIdentifier}");
  }

  /// Processa lista de mensagens (quando carrega conversa)
  void _handleMessagesList(dynamic data) {
    final List<dynamic> messageListRaw = data['messages'] as List<dynamic>;
    final messages = messageListRaw
        .map((msg) => ChatMessageEntity.fromMap(msg as Map<String, dynamic>))
        .toList();

    messages.sort((a, b) => a.createdAt.compareTo(b.createdAt));

    // Atualiza as mensagens atuais no ChatStore
    _chatStore.updateCurrentMessages(messages);

    debugPrint(
        "ChatSocketHandler: Lista de mensagens atualizada (${messages.length} mensagens)");
  }

  /// Processa mensagem enviada
  void _handleMessageSent(dynamic data) {
    final sentMessage = ChatMessageEntity.fromMap(data as Map<String, dynamic>);

    // Adiciona ou atualiza a mensagem no store
    _chatStore.addMessage(sentMessage);

    debugPrint("ChatSocketHandler: Mensagem enviada confirmada");
  }

  /// Processa atualização de mensagens lidas
  void _handleMessagesUpdated(dynamic data) {
    // Aqui você pode implementar lógica adicional se necessário
    // Por exemplo, atualizar status de leitura das mensagens
    debugPrint("ChatSocketHandler: Mensagens marcadas como lidas");
  }

  /// Processa atualização de contadores de mensagens não lidas
  void _handleUnreadCountUpdated(dynamic data) {
    final userUnreadCounts = data as List<dynamic>?;

    if (userUnreadCounts != null) {
      for (var userCount in userUnreadCounts) {
        final userIdentifier = dynamicToString(userCount['sender_identifier']);
        final count = dynamicToInt(userCount['count']);
        _chatStore.updateUnreadCount(userIdentifier, count);
      }

      debugPrint(
          "ChatSocketHandler: Contadores de mensagens não lidas atualizados");
    }
  }

  /// Chamado quando o socket conecta
  void _onSocketConnected() {
    debugPrint("ChatSocketHandler: Socket conectado - handlers de chat ativos");
    // Aqui você pode solicitar contadores atualizados se necessário
  }

  /// Chamado quando o socket desconecta
  void _onSocketDisconnected() {
    debugPrint("ChatSocketHandler: Socket desconectado");
    // Aqui você pode limpar dados temporários se necessário
  }

  /// Mostra notificação local se o app estiver em background
  void _showNotificationIfAppInBackground(ChatMessageEntity message) async {
    try {
      if (!_appLifecycleService.isInBackground) {
        return;
      }
      debugPrint(
          "ChatSocketHandler: Verificando se deve mostrar notificação...");
      debugPrint(
          "ChatSocketHandler: App lifecycle state: ${_appLifecycleService.currentState}");
      debugPrint(
          "ChatSocketHandler: Is in background: ${_appLifecycleService.isInBackground}");

      // Para iOS em release, usa uma abordagem mais agressiva
      // Sempre mostra notificação exceto se estiver explicitamente na tela de chat
      bool isInChatScreen = false;
      try {
        isInChatScreen = Modular.to.path == AppRoutes.friendChat;
      } catch (e) {
        debugPrint("ChatSocketHandler: Erro ao verificar rota atual: $e");
        isInChatScreen = false;
      }

      // Se está na tela de chat, não mostra notificação
      if (isInChatScreen) {
        debugPrint(
            "ChatSocketHandler: Está na tela de chat, não mostrando notificação");
        return;
      }

      // Para iOS em release, sempre mostra notificação se não estiver na tela de chat
      debugPrint("ChatSocketHandler: Preparando notificação...");

      // Obtém o nome do remetente
      String senderName = message.sender?.name ?? 'Usuário';

      // Se não temos o nome do sender na mensagem, usa o identifier
      if (senderName == 'Usuário' && message.senderIdentifier.isNotEmpty) {
        senderName = message.senderIdentifier;
      }

      // Cria o título e corpo da notificação
      String title = senderName;
      String body = message.content;

      debugPrint("ChatSocketHandler: Título: $title, Corpo: $body");

      // Cria payload com informações da mensagem
      Map<String, dynamic> notificationData = {
        'type': 'CHAT_MESSAGE',
        'title': title,
        'message': body,
        'data': {
          'sender_identifier': message.senderIdentifier,
          'receiver_identifier': message.receiverIdentifier,
          'message_id': message.id,
          'created_at': message.createdAt.toIso8601String(),
        }
      };

      String payload = json.encode(notificationData);

      debugPrint(
          "ChatSocketHandler: Chamando LocalNotificationService.showNotification...");

      // Mostra a notificação imediatamente
      await LocalNotificationService.showNotification(
          message.id, title, body, payload);

      debugPrint(
          "ChatSocketHandler: Comando de notificação enviado para mensagem de $senderName");
    } catch (e) {
      debugPrint("ChatSocketHandler: Erro ao mostrar notificação: $e");
    }
  }

  /// Verifica se os handlers estão inicializados
  bool get isInitialized => _isInitialized;
}
