import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:play_to_vibe/app/theme/colors.dart';
import 'package:play_to_vibe/core/utils/assets_strings.dart';

class HistoryFriendCard extends StatelessWidget {
  final Color timeTextColor;
  final String timeText;
  final String answer;
  final String response;
  final int pontuation;

  const HistoryFriendCard(
      {super.key,
      this.timeTextColor = Colors.white,
      this.timeText = '',
      this.answer = '',
      this.response = '',
      this.pontuation = 0});

  Widget getAnswer() {
    return Container(
      width: 280,
      margin: const EdgeInsets.symmetric(vertical: 18),
      padding: const EdgeInsets.symmetric(horizontal: 28),
      child: Center(
        child: Text(
          answer,
          textAlign: TextAlign.center,
          style: const TextStyle(
              letterSpacing: 0.25,
              height: 1.1,
              fontFamily: "Roboto",
              fontWeight: FontWeight.bold,
              color: CustomColors.orangeSoda,
              fontSize: 16),
        ),
      ),
    );
  }

  Widget getResponse() {
    return Container(
      width: 280,
      padding: const EdgeInsets.symmetric(vertical: 9, horizontal: 8),
      constraints: const BoxConstraints(minHeight: 50),
      decoration: BoxDecoration(
          border: Border.all(width: 1, color: Colors.grey.shade200),
          borderRadius: const BorderRadius.all(Radius.circular(53))),
      child: Center(
        child: Text(
          response,
          textAlign: TextAlign.center,
          style: const TextStyle(
              letterSpacing: 0.25,
              height: 1.1,
              fontFamily: "Roboto",
              fontWeight: FontWeight.w500,
              color: CustomColors.americanPurple2,
              fontSize: 16),
        ),
      ),
    );
  }

  Widget getPontuation() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        SvgPicture.asset(Assets.svg.fire3, width: 16),
        const SizedBox(width: 4),
        Text(
          '+ $pontuation',
          style: const TextStyle(
              letterSpacing: 0.1,
              fontFamily: "Roboto",
              fontWeight: FontWeight.bold,
              color: CustomColors.americanPurple2,
              fontSize: 10),
        )
      ],
    );
  }

  Widget getWidget() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Container(
          width: 326,
          padding: const EdgeInsets.only(bottom: 4, right: 18),
          child: Text(
            timeText,
            textAlign: TextAlign.end,
            style: TextStyle(
                fontFamily: "Roboto",
                fontWeight: FontWeight.bold,
                color: timeTextColor,
                fontSize: 8),
          ),
        ),
        Container(
            width: 326,
            constraints: const BoxConstraints(minHeight: 165),
            decoration: BoxDecoration(
                color: Colors.white,
                border: Border.all(width: 1, color: Colors.white),
                borderRadius: const BorderRadius.all(Radius.circular(30))),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                getAnswer(),
                getResponse(),
                Padding(
                  padding: const EdgeInsets.all(18.0),
                  child: getPontuation(),
                ),
              ],
            )),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(width: 326, child: getWidget());
  }
}
