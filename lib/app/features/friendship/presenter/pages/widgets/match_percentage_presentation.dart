import 'dart:async';
import 'dart:math';

import 'package:flutter/material.dart';
import 'package:play_to_vibe/app/features/friendship/presenter/pages/widgets/circular_gradient_progress_indicator.dart';
import 'package:play_to_vibe/app/theme/colors.dart';
import 'package:play_to_vibe/app/widgets/custom_image.dart';
import 'package:play_to_vibe/core/utils/assets_strings.dart';
import 'package:play_to_vibe/core/utils/interval_utils.dart';
import 'package:sizer/sizer.dart';

class MatchPercentagePresentation extends StatefulWidget {
  final bool animate;
  final bool informationDisplayEnabled;
  final double progress;
  final int percentage;
  final Color? borderColor;
  final List<Color> backgroundColors;
  final Color backgroundColor;
  final double size;
  final double? largeTextSize;
  final double? smallTextSize;

  const MatchPercentagePresentation({
    super.key,
    this.size = 100,
    this.borderColor,
    this.informationDisplayEnabled = true,
    this.backgroundColor = CustomColors.vividTangerine,
    this.animate = true,
    this.progress = 0.0,
    this.largeTextSize,
    this.smallTextSize,
    this.percentage = 1,
    this.backgroundColors = const [],
  });

  @override
  State<MatchPercentagePresentation> createState() =>
      _MatchPercentagePresentationState();
}

class _MatchPercentagePresentationState
    extends State<MatchPercentagePresentation> {
  int valorAleatorio = 0;
  int _animatedPercentage = 0;
  int times = 0;
  int get _percentage =>
      widget.animate ? _animatedPercentage : widget.percentage;

  void _updateAnimatedLocalValues(Timer timer) {
    setState(() {
      _animatedPercentage = Random().nextInt(101);
      if (!widget.animate) {
        timer.cancel();
      }
    });
  }

  void _intervalForValueAnimations() {
    setInterval(
        callback: _updateAnimatedLocalValues,
        duration: Duration(milliseconds: 200));
  }

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    _intervalForValueAnimations();
  }

  Widget _buildCenterInfo() {
    return Container(
      height: widget.size * .87,
      width: widget.size * .87,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: widget.backgroundColor,
      ),
      child: Opacity(
        opacity: widget.informationDisplayEnabled ? 1 : 0.2,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              'YOU ARE',
              style: TextStyle(
                  fontFamily: 'Roboto',
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                  fontSize: widget.smallTextSize ?? 23.sp,
                  letterSpacing: 1,
                  decoration: TextDecoration.none),
            ),
            SizedBox(
              height: 1.42.h,
            ),
            Text(
              '$_percentage%',
              style: TextStyle(
                  fontFamily: 'AlbertSans',
                  color: Colors.white,
                  fontWeight: FontWeight.w800,
                  fontSize: widget.largeTextSize ?? 38.sp,
                  letterSpacing: .5,
                  height: 0.8,
                  decoration: TextDecoration.none),
            ),
            SizedBox(
              height: 1.42.h,
            ),
            Text(
              'IN SYNC',
              style: TextStyle(
                  fontFamily: 'Roboto',
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                  letterSpacing: 1,
                  fontSize: widget.smallTextSize ?? 23.sp,
                  decoration: TextDecoration.none),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBlockOverlay() {
    if (widget.informationDisplayEnabled) {
      return Container();
    }
    return Container(
      height: widget.size * .87,
      width: widget.size * .87,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: Colors.white.withAlpha(80),
      ),
      child: CustomImage(
        Assets.svg.overlay.playMoreToUnlickVibeCheck,
        height: 50,
      ),
    );
  }

  Widget _buildContent() {
    return Stack(
      children: [_buildCenterInfo(), _buildBlockOverlay()],
    );
  }

  @override
  Widget build(BuildContext context) {
    final borderColor = widget.informationDisplayEnabled
        ? widget.borderColor
        : Colors.transparent;
    return CircularGradientProgressIndicator(
        progress: widget.progress,
        color: borderColor,
        backgroundColors: widget.backgroundColors,
        progressAnimationDuration: const Duration(seconds: 7),
        animatedBackground: widget.animate,
        size: widget.size,
        child: _buildContent());
  }
}

// class MatchPercentagePresentation extends StatelessWidget {
//   final bool animate;
//   final double progress;
//   final int percentage;
//   final Color? borderColor;
//   final Color backgroundColor;
//   final double size;
//   final double? largeTextSize;
//   final double? smallTextSize;

//   const MatchPercentagePresentation({
//     super.key,
//     this.size = 100,
//     this.borderColor,
//     this.backgroundColor = CustomColors.vividTangerine,
//     this.animate = true,
//     this.progress = 0.0,
//     this.largeTextSize,
//     this.smallTextSize,
//     this.percentage = 1,
//   });

//   @override
//   Widget build(BuildContext context) {
//     return CircularGradientProgressIndicator(
//       progress: progress,
//       color: borderColor,
//       animationDuration: const Duration(milliseconds: 1500),
//       animatedBackground: animate,
//       size: size,
//       child: Container(
//         height: size * .87,
//         width: size * .87,
//         decoration: BoxDecoration(
//           shape: BoxShape.circle,
//           color: backgroundColor,
//         ),
//         child: Column(
//           mainAxisSize: MainAxisSize.min,
//           mainAxisAlignment: MainAxisAlignment.center,
//           children: [
//             Text(
//               'YOU ARE',
//               style: TextStyle(
//                   fontFamily: 'Roboto',
//                   color: Colors.white,
//                   fontWeight: FontWeight.bold,
//                   fontSize: smallTextSize ?? 23.sp,
//                   letterSpacing: 1,
//                   decoration: TextDecoration.none),
//             ),
//             SizedBox(
//               height: 1.42.h,
//             ),
//             Text(
//               '$percentage%',
//               style: TextStyle(
//                   fontFamily: 'AlbertSans',
//                   color: Colors.white,
//                   fontWeight: FontWeight.w800,
//                   fontSize: largeTextSize ?? 45.sp,
//                   letterSpacing: .5,
//                   height: 0.8,
//                   decoration: TextDecoration.none),
//             ),
//             SizedBox(
//               height: 1.42.h,
//             ),
//             Text(
//               'IN SYNC',
//               style: TextStyle(
//                   fontFamily: 'Roboto',
//                   color: Colors.white,
//                   fontWeight: FontWeight.bold,
//                   letterSpacing: 1,
//                   fontSize: smallTextSize ?? 23.sp,
//                   decoration: TextDecoration.none),
//             ),
//           ],
//         ),
//       ),
//     );
//   }
// }
