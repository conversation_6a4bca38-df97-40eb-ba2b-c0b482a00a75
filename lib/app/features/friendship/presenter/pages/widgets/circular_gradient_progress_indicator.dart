import 'package:flutter/material.dart';
import 'package:play_to_vibe/app/theme/colors.dart';
import 'dart:math';

class CircularGradientProgressIndicator extends StatefulWidget {
  final double progress;
  final Duration progressAnimationDuration;
  final double startProgressValue;
  final double endProgressValue;
  final List<BoxShadow>? boxShadow;
  final bool animatedBackground;
  final BoxBorder? border;
  final double size;
  final Color? color;
  final Widget? child;
  final List<Color> backgroundColors;

  const CircularGradientProgressIndicator({
    super.key,
    required this.progress,
    required this.progressAnimationDuration,
    this.animatedBackground = true,
    this.border,
    this.boxShadow,
    this.size = 150,
    this.startProgressValue = 0,
    this.endProgressValue = 1,
    this.color,
    this.backgroundColors = const [],
    this.child,
  });

  @override
  _CircularGradientProgressIndicatorState createState() =>
      _CircularGradientProgressIndicatorState();
}

class _CircularGradientProgressIndicatorState
    extends State<CircularGradientProgressIndicator>
    with TickerProviderStateMixin {
  late AnimationController _progressController;
  Animation<double>? _progressAnimation;
  late AnimationController _colorController;
  late AnimationController _gradientRotationController;
  late Animation<Color?> _colorAnimation;

  final List<Color> colors = [
    CustomColors.primary,
    CustomColors.mauvelous,
    CustomColors.maximumYellowRed,
    CustomColors.mediumSeaGreen,
    CustomColors.cadmiumViolet,
  ];

  double get progress => widget.animatedBackground && _progressAnimation != null
      ? _progressAnimation!.value
      : widget.progress;

  List<Color> get backgroundColors {
    if (widget.backgroundColors.isEmpty) {
      return List<Color>.filled(5, Colors.transparent);
    } else if (widget.backgroundColors.length == 1) {
      return List<Color>.filled(5, widget.backgroundColors[0]);
    } else if (widget.backgroundColors.length == 2) {
      return [
        widget.backgroundColors[0],
        widget.backgroundColors[0],
        widget.backgroundColors[1],
        widget.backgroundColors[1],
        widget.backgroundColors[0],
      ];
    } else if (widget.backgroundColors.length == 3) {
      return [
        widget.backgroundColors[0],
        widget.backgroundColors[1],
        widget.backgroundColors[2],
        widget.backgroundColors[2],
        widget.backgroundColors[0],
      ];
    } else {
      return widget.backgroundColors;
    }
  }

  @override
  void initState() {
    super.initState();

    _progressController = AnimationController(
      vsync: this,
      duration: widget.progressAnimationDuration,
    );

    _progressAnimation = Tween<double>(
      begin: widget.startProgressValue,
      end: widget.endProgressValue,
    ).animate(
      CurvedAnimation(
        parent: _progressController,
        curve: Curves.easeInOut,
      ),
    );

    _progressController.forward();

    _colorController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 4),
    )..repeat(reverse: true);

    _colorAnimation = TweenSequence<Color?>(
      List.generate(
        colors.length - 1,
        (index) => TweenSequenceItem<Color?>(
          tween: ColorTween(begin: colors[index], end: colors[index + 1]),
          weight: 1.0,
        ),
      ),
    ).animate(_colorController);

    _colorController.addListener(() {
      setState(() {});
    });

    _gradientRotationController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 5),
    )..repeat();

    _gradientRotationController.addListener(() {
      setState(() {});
    });
  }

  getExternalBoxShadow(Color color) => widget.animatedBackground
      ? [
          BoxShadow(
            color: color.withValues(alpha: 0.6),
            blurRadius: 35,
            spreadRadius: 4,
          ),
        ]
      : null;

  getInternalBoxShadow(Color color) => widget.animatedBackground
      ? [
          BoxShadow(
            color: color.withValues(alpha: 0.8),
            blurRadius: 38,
            spreadRadius: 3,
          )
        ]
      : null;
  @override
  void dispose() {
    _progressController.dispose();
    _colorController.dispose();
    _gradientRotationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: Listenable.merge(
          [_progressAnimation, _colorAnimation, _gradientRotationController]),
      builder: (context, child) {
        double rotationAngle = _gradientRotationController.value * 2 * pi;
        return SizedBox(
          width: widget.size,
          height: widget.size,
          child: Stack(
            alignment: Alignment.center,
            children: [
              CustomPaint(
                size: Size(widget.size, widget.size),
                painter: _CircularProgressPainter(
                    progress,
                    widget.animatedBackground,
                    widget.color ??
                        (_colorAnimation.value ?? Colors.transparent),
                    rotationAngle,
                    widget.color,
                    backgroundColors),
              ),
              Container(
                width: widget.size * 0.87,
                height: widget.size * 0.87,
                decoration: BoxDecoration(
                  boxShadow: getInternalBoxShadow(
                      _colorAnimation.value ?? Colors.transparent),
                  shape: BoxShape.circle,
                  color: Colors.transparent,
                ),
                child: widget.child != null
                    ? ClipOval(
                        child: SizedBox(
                          width: widget.size * 0.87,
                          height: widget.size * 0.87,
                          child: FittedBox(
                            fit: BoxFit.scaleDown,
                            child: widget.child,
                          ),
                        ),
                      )
                    : null,
              ),
            ],
          ),
        );
      },
    );
  }
}

class _CircularProgressPainter extends CustomPainter {
  final double progress;
  final Color backgroundColor;
  final bool animatedBackground;
  final double rotationAngle;
  final Color? solidColor;
  final List<Color> backgroundColors;

  _CircularProgressPainter(
      this.progress,
      this.animatedBackground,
      this.backgroundColor,
      this.rotationAngle,
      this.solidColor,
      this.backgroundColors);

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final strokeWidth = size.height * .01;
    final radius = min(size.width, size.height) / 2;
    final progressShadowRadius = radius * 0.97;

    if (animatedBackground) {
      final gradientCenter = Alignment(
        cos(rotationAngle) * 0.7,
        sin(rotationAngle) * 0.7,
      );

      final backgroundGradient = RadialGradient(
        colors: [backgroundColor, CustomColors.lightSilver],
        stops: [0.55, 1],
        center: gradientCenter,
      );

      final rect = Rect.fromCircle(center: center, radius: radius);
      final paint = Paint()..shader = backgroundGradient.createShader(rect);
      canvas.drawCircle(center, min(size.width, size.height) / 2, paint);

      final shadowPaint = Paint()
        ..color = backgroundColor
        ..style = PaintingStyle.stroke
        ..strokeWidth = 15
        ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 4);

      canvas.drawArc(
        Rect.fromCircle(center: center, radius: progressShadowRadius),
        -pi / 2,
        2 * pi,
        false,
        shadowPaint,
      );
    } else if (solidColor != null) {
      final paint = Paint()..color = solidColor!;
      canvas.drawCircle(center, min(size.width, size.height) / 2, paint);
    } else {
      final backgroundGradient = SweepGradient(
          colors: backgroundColors,
          stops: [0, 0.23, 0.5, 0.745, 1],
          center: Alignment.center);

      final rect = Rect.fromCircle(center: center, radius: radius);
      final paint = Paint()..shader = backgroundGradient.createShader(rect);
      canvas.drawCircle(center, min(size.width, size.height) / 2, paint);
    }

    final progressShadow = Paint()
      ..color = Colors.transparent
      ..style = PaintingStyle.stroke
      ..strokeWidth = .5
      ..strokeCap = StrokeCap.round;

    canvas.drawArc(
      Rect.fromCircle(center: center, radius: progressShadowRadius),
      -pi / 2,
      2 * pi,
      false,
      progressShadow,
    );

    final gradient = SweepGradient(
      startAngle: -pi / 2,
      endAngle: 4 * pi / 2,
      colors: [Colors.white.withAlpha(140)],
      stops: [1.0],
    );

    final progressPaint = Paint()
      ..shader = gradient.createShader(
        Rect.fromCircle(center: center, radius: radius),
      )
      ..style = PaintingStyle.stroke
      ..strokeWidth = strokeWidth
      ..strokeCap = StrokeCap.round;

    final angle = 2 * pi * progress;

    canvas.drawArc(
      Rect.fromCircle(center: center, radius: radius),
      -pi / 2,
      angle,
      false,
      progressPaint,
    );
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return true;
  }
}
