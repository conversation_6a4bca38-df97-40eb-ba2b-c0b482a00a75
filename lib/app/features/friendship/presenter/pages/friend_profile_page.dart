import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:flutter_triple/flutter_triple.dart';
import 'package:play_to_vibe/app/features/friendship/domain/entities/friendship_profile_entity.dart';
import 'package:play_to_vibe/app/features/friendship/domain/entities/friendship_store_entity.dart';
import 'package:play_to_vibe/app/features/friendship/presenter/controllers/friendship_controller.dart';
import 'package:play_to_vibe/app/features/friendship/presenter/pages/widgets/history_friend_card.dart';
import 'package:play_to_vibe/app/features/friendship/presenter/store/friendship_store.dart';
import 'package:play_to_vibe/app/services/remote_config/remote_config_store.dart';
import 'package:play_to_vibe/app/theme/colors.dart';
import 'package:play_to_vibe/app/widgets/app_header.dart';
import 'package:play_to_vibe/app/widgets/avatar.dart';
import 'package:play_to_vibe/app/widgets/button.dart';
import 'package:play_to_vibe/app/widgets/gradient_container.dart';
import 'package:play_to_vibe/app/widgets/quycky_modal_progress.dart';
import 'package:play_to_vibe/core/utils/app_routes.dart';
import 'package:play_to_vibe/core/utils/image_assets.dart';
import 'package:play_to_vibe/core/utils/interval_utils.dart';
import 'package:play_to_vibe/core/utils/quycky_icons_icons.dart';
import 'package:play_to_vibe/core/utils/show_message.dart';
import 'package:sizer/sizer.dart';

class FriendProfilePage extends StatefulWidget {
  final String id;
  final String title;

  const FriendProfilePage(
      {super.key, this.title = 'FriendProfilePage', required this.id});

  @override
  FriendProfilePageState createState() => FriendProfilePageState();
}

class FriendProfilePageState extends State<FriendProfilePage> {
  final _remoteConfig = Modular.get<RemoteConfigStore>();
  final FriendshipController _controller = Modular.get<FriendshipController>();
  final FriendshipStore _store = Modular.get<FriendshipStore>();
  final List<FriendshipAnswerEntity> defaultAnswers = [
    FriendshipAnswerEntity(
        id: '1',
        createdAt: DateTime.now(),
        points: [],
        questionId: '',
        roomId: '',
        userId: '',
        value: '',
        updatedAt: DateTime.now(),
        room: FriendshipRoomEntity(
            createdAt: DateTime.now(),
            howManyPeople: 4,
            id: '',
            isBloqued: false,
            name: '',
            questionTimeInSeconds: 8,
            responseTimeInSeconds: 8,
            updatedAt: DateTime.now()),
        question: FriendshipQuestionEntity(
            isActived: 1,
            updatedAt: DateTime.now(),
            createdAt: DateTime.now(),
            description: '',
            id: '',
            stage: 1)),
  ];
  @override
  void initState() {
    super.initState();
    setTimeout(
        callback: () => _controller.getFriendProfile(int.parse(widget.id)),
        duration: Duration(seconds: 1));
  }

  onAccept(int id) => _controller.acceptInvite(id);

  onCancel(int id) => _controller.rejectInvite(id);
  Widget getQuestionBallon(String question) {
    return Padding(
      padding: const EdgeInsets.only(right: 80),
      child: Container(
        padding: const EdgeInsets.only(left: 8),
        decoration: const BoxDecoration(
          color: Colors.white30,
          borderRadius: BorderRadius.all(Radius.circular(100)),
        ),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 4.0),
          child: ListTile(
            title: Align(
              alignment: Alignment.centerLeft,
              child: Text(
                question,
                textAlign: TextAlign.center,
                style: const TextStyle(
                    fontFamily: "SFProText",
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                    fontSize: 15),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget getAnswerBallon(FriendshipAnswerEntity item) {
    return Padding(
        padding: const EdgeInsets.only(top: 8, left: 80),
        child: Container(
            constraints: const BoxConstraints(maxWidth: 500),
            child: Column(children: [
              Container(
                constraints: const BoxConstraints(minHeight: 67),
                child: Stack(
                    alignment: AlignmentDirectional.bottomCenter,
                    children: [
                      Container(
                        constraints: const BoxConstraints(minHeight: 50),
                        decoration: const BoxDecoration(
                          color: CustomColors.orangeSoda40,
                          borderRadius: BorderRadius.all(Radius.circular(100)),
                        ),
                        child: Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 12.0),
                          child: Padding(
                            padding: const EdgeInsets.all(14.0),
                            child: ListTile(
                              title: Text(
                                item.value,
                                style: const TextStyle(
                                    fontFamily: "SFProText",
                                    fontWeight: FontWeight.bold,
                                    color: Colors.white,
                                    fontSize: 15),
                              ),
                            ),
                          ),
                        ),
                      ),
                      const Positioned(
                          bottom: 75,
                          left: 30,
                          child: Text(
                            '',
                            style: TextStyle(
                                fontFamily: "SFProText",
                                fontWeight: FontWeight.bold,
                                color: Colors.white,
                                fontSize: 7),
                          )),
                      Positioned(
                          bottom: 0,
                          left: 0,
                          child: Row(
                            children: [
                              Padding(
                                padding: const EdgeInsets.only(right: 3),
                                child: Image.asset(
                                  ImageAssets.fire,
                                  width: 13,
                                ),
                              ),
                              Text(
                                item.points.length.toString(),
                                style: const TextStyle(
                                    fontFamily: "SFProText",
                                    fontWeight: FontWeight.bold,
                                    color: Colors.white,
                                    fontSize: 7),
                              )
                            ],
                          )),
                      Positioned(
                          bottom: 0,
                          right: 0,
                          child: Text(
                            getWhenAnsweredLabel(item.createdAt),
                            style: const TextStyle(
                                fontFamily: "SFProText",
                                fontWeight: FontWeight.bold,
                                color: Colors.white,
                                fontSize: 7),
                          )),
                    ]),
              )
            ])));
  }

  int getPontuation(List<FriendshipPointEntity> points) {
    int res = 0;
    final pontuation = _remoteConfig.state.pontuation;

    for (int i = 0; i < points.length; i++) {
      res += points[i].pointTypeId == 1 ? pontuation.fire : pontuation.ice;
    }

    return res;
  }

  Widget getListItem(int index, FriendshipAnswerEntity item) {
    return Padding(
        padding: const EdgeInsets.only(top: 18),
        child: HistoryFriendCard(
            timeText: getWhenAnsweredLabel(item.createdAt),
            pontuation: getPontuation(item.points),
            answer: item.question.description,
            response: item.value,
            timeTextColor: Colors.white));
  }

  void _goToInviteFriendToAGame() {
    Modular.to.pushReplacementNamed(AppRoutes.gameLobby(), arguments: {
      'friend': _store.state.friendProfile?.user,
    });
  }

  String getWhenAnsweredLabel(DateTime date) {
    String result = "";
    final now = DateTime.now();
    Duration durationDifference = now.difference(date);

    if (durationDifference.inMinutes < 60) {
      result = "just now";
    } else if (durationDifference.inMinutes < 1440) {
      result = durationDifference.inHours > 1
          ? "${durationDifference.inHours} hours ago"
          : "${durationDifference.inHours} hour ago";
    } else {
      result = "1 day ago";
    }
    return result;
  }

  void handleRemoveFriend() async {
    _controller.removeFriend(int.parse(widget.id)).then((result) {
      if (result) {
        ShowMessage(
            noAvatar: true,
            message: 'The friend was removed successfully',
            onPressed: handleBack());
        return;
      }

      ShowMessage(
          noAvatar: true,
          message:
              'There was a problem removing the friend, please try again later',
          onPressed: handleBack());
    });
  }

  Widget _getBottomButtons() {
    return Column(
      children: [
        SizedBox(
            height: 6.h,
            width: 83.34.w,
            child: Button(
                autoSized: true,
                onPressed: _goToInviteFriendToAGame,
                text: "INVITE TO PLAY")),
        SizedBox(
          height: 1.78.h,
        ),
        SizedBox(
            height: 6.h,
            width: 83.34.w,
            child: Button(
                autoSized: true,
                outlined: true,
                borderColor: Colors.white,
                onPressed: handleRemoveFriend,
                text: "UNMATCH"))
      ],
    );
  }

  Widget getList(List<FriendshipAnswerEntity> answers) {
    final List<Widget> childrenItems = [];
    answers.take(3).forEach((element) {
      childrenItems.add(getListItem(answers.indexOf(element), element));
    });
    return Material(
      color: Colors.transparent,
      child: Container(
          height: 516,
          constraints: const BoxConstraints(maxWidth: 400),
          child: SingleChildScrollView(
            child: Column(
              children: childrenItems,
            ),
          )),
    );
  }

  Widget _getHasNoAnswersPlayMore(FriendProfileEntity? friendProfileData) {
    final playerName = friendProfileData?.user.name ?? '';
    return Center(
      child: RichText(
          textAlign: TextAlign.center,
          text: TextSpan(
            style: TextStyle(
              color: Colors.white,
              fontSize: 14,
              height: 1.4,
              letterSpacing: 0.25,
              fontFamily: 'Roboto',
            ),
            children: <TextSpan>[
              TextSpan(
                  text: playerName,
                  style: TextStyle(fontWeight: FontWeight.w700)),
              TextSpan(
                text: ' has not played any game yet,\n',
                style: TextStyle(
                    fontFamily: 'Roboto', fontWeight: FontWeight.normal),
              ),
              TextSpan(text: 'Return later to see their history'),
            ],
          )),
    );
  }

  Widget _getContent(FriendProfileEntity? friendProfileData) {
    return SizedBox(
        height: 40.h,
        child: friendProfileData?.answers != null &&
                friendProfileData?.answers.isNotEmpty == true
            ? getList(friendProfileData!.answers)
            : _getHasNoAnswersPlayMore(friendProfileData));
  }

  void handleOpenFriendMatch() =>
      Modular.to.pushNamed(AppRoutes.checkFriendMatch, arguments: {
        'friendId': _store.state.friendProfile?.user.id.toString(),
        'isNewFriend': false
      });

  handleBack() => Modular.to.pop();

  Widget getWidget(FriendProfileEntity? friendProfileData) {
    return Column(
      children: [
        AppHeader(
          logoSectionLeftWidget: IconButton(
              onPressed: handleBack,
              icon: const Icon(QuyckyIcons.arrow_left_circle,
                  color: Colors.white)),
          logoSectionRightWidget: IconButton(
              onPressed: handleOpenFriendMatch,
              icon: const Icon(
                QuyckyIcons.heart_search,
                color: Colors.white,
                size: 24,
              )),
          title: 'FRIEND PROFILE',
        ),
        Padding(
          padding: const EdgeInsets.only(top: 14),
          child: Avatar(
            imagePath: friendProfileData?.user.avatarUrl,
            addPhotoButton: false,
            size: 100,
          ),
        ),
        Padding(
          padding: const EdgeInsets.only(top: 12.0),
          child: Text(
            friendProfileData?.user.name ?? '',
            textAlign: TextAlign.center,
            style: const TextStyle(
                letterSpacing: 1.2,
                fontFamily: "Roboto",
                fontWeight: FontWeight.bold,
                color: Colors.white,
                fontSize: 12),
          ),
        ),
        SizedBox(height: 1.h),
        _getContent(friendProfileData),
        SizedBox(height: 1.77.h),
        _getBottomButtons(),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: GradientContainer(
        useDefault: true,
        normalOpacity: 0,
        hotOpacity: 0,
        coldOpacity: 0,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.only(top: 0),
            child: TripleBuilder(
                store: _store,
                builder: (context, tripleObj) {
                  Triple<FriendshipStoreEntity> triple =
                      tripleObj as Triple<FriendshipStoreEntity>;
                  return QuyckyModalProgress(
                    state: triple.isLoading,
                    child: getWidget(triple.state.friendProfile),
                  );
                }),
          ),
        ),
      ),
    );
  }
}
