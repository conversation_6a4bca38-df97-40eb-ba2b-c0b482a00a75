import 'dart:ui';
import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:flutter_triple/flutter_triple.dart';
import 'package:intl/intl.dart'; // Para formatação de data/hora
import 'package:play_to_vibe/app/features/friendship/domain/entities/chat_message_entity.dart'; // Importando a entidade
import 'package:play_to_vibe/app/features/friendship/domain/entities/report_player_params_entity.dart';
import 'package:play_to_vibe/app/features/friendship/presenter/controllers/friendship_controller.dart';
import 'package:play_to_vibe/app/features/friendship/presenter/services/chat_socket_handler.dart';
import 'package:play_to_vibe/app/widgets/dialog_friend_menu.dart';
import 'package:play_to_vibe/core/enumerators/egame_action.dart'; // Será usado para o 'action' base do socket
import 'package:play_to_vibe/app/features/user/domain/entities/user_entity.dart';
import 'package:play_to_vibe/app/features/user/presenter/store/user_store.dart';
import 'package:play_to_vibe/app/services/game_socket/game_socket_service.dart';
import 'package:play_to_vibe/app/theme/colors.dart';
// Novos imports para DTOs e Enumerator
import 'package:play_to_vibe/app/features/friendship/domain/dtos/chat_message_dto.dart';
import 'package:play_to_vibe/app/features/friendship/domain/dtos/send_message_dto.dart';
import 'package:play_to_vibe/app/features/friendship/domain/dtos/get_messages_between_users_dto.dart';
import 'package:play_to_vibe/app/features/friendship/domain/dtos/mark_as_read_dto.dart';
import 'package:play_to_vibe/app/features/friendship/domain/dtos/get_unread_count_dto.dart';
import 'package:play_to_vibe/app/features/friendship/presenter/store/chat_store.dart';
import 'package:play_to_vibe/app/features/friendship/domain/enumerators/chat_action_enumerator.dart';
import 'package:play_to_vibe/app/widgets/app_header.dart';
import 'package:play_to_vibe/app/widgets/avatar.dart'; // Importando o Avatar
import 'package:play_to_vibe/app/widgets/custom_image.dart';
import 'package:play_to_vibe/app/widgets/friend_options_bottom_sheet.dart';
import 'package:play_to_vibe/app/widgets/gradient_container.dart';
import 'package:play_to_vibe/app/widgets/report_player_dialog.dart'; // Re-adicionado
import 'package:play_to_vibe/app/widgets/block_player_dialog.dart'; // Re-adicionado
import 'package:play_to_vibe/app/widgets/social_login_buttons.dart';
import 'package:play_to_vibe/core/entities/abs_mappable.dart';
import 'package:play_to_vibe/core/utils/app_routes.dart';
import 'package:play_to_vibe/core/utils/assets_strings.dart';
import 'package:play_to_vibe/core/utils/interval_utils.dart';
import 'package:play_to_vibe/core/utils/quycky_icons_icons.dart';
import 'package:play_to_vibe/core/utils/show_dialog_friend_menu.dart';
import 'package:play_to_vibe/core/utils/show_message.dart';
import 'package:sizer/sizer.dart';
import 'package:uuid/uuid.dart'; // Para gerar IDs únicos

// A classe Teste não é mais necessária, será removida.

class FriendChat extends StatefulWidget {
  final UserEntity friend;
  const FriendChat({super.key, required this.friend});

  @override
  State<FriendChat> createState() => _FriendChatState();
}

class _FriendChatState extends State<FriendChat> {
  final _chatSocketHandler = Modular.get<ChatSocketHandler>();
  final _controller = Modular.get<FriendshipController>();
  final _userStore = Modular.get<UserStore>();
  final _chatStore = Modular.get<ChatStore>();
  bool _showInfoBanner = true;
  final TextEditingController _messageController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  final double _messageTextFieldBorderRadius = 122;
  final _uuid = Uuid();
  bool _isLoading = false;
  String? _connectionId; // Para armazenar o connectionId do socket
  final _socketService = Modular.get<GameSocketService>();

  bool get isLoading => _isLoading;
  set isLoading(bool value) {
    setState(() {
      _isLoading = value;
    });
  }

  bool get showInfoBanner => _showInfoBanner && _messages.isEmpty;

  bool get _isUserSocialLogged =>
      _userStore.state.user.appleId?.isNotEmpty ==
          true || // Adicionado null check
      _userStore.state.user.googleId?.isNotEmpty ==
          true; // Adicionado null check

  set showInfoBanner(bool value) {
    setState(() {
      _showInfoBanner = false;
    });
  }

  // As mensagens agora são obtidas diretamente do ChatStore
  List<ChatMessageEntity> get _messages => _chatStore.state.currentMessages;

  @override
  void initState() {
    super.initState();
    _initializeChat();
  }

  @override
  void dispose() {
    // Os handlers globais não precisam ser removidos aqui
    // pois são gerenciados pelo ChatSocketHandler
    _messageController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  Future<void> _initializeChat() async {
    if (_socketService.isConnected) {
      _chatStore.setCurrentConversationFriendIdentifier(
          widget.friend.identifier ?? '');
      _connectionId = _socketService.connectionId;
      _fetchInitialMessages();
    }
  }

  // Métodos de manipulação de chat removidos - agora tratados globalmente pelo ChatSocketHandler

  void _scrollToBottom() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  void _sendMessage() {
    if (_messageController.text.isEmpty) {
      print(
          "Não é possível enviar mensagem: texto vazio ou connectionId inválido.");
      return;
    }

    // final user = _userStore.state.user;

    final sendMessageData = SendMessageDTO(
      receiverIdentifier: widget.friend.identifier ?? '',
      content: _messageController.text,
    );

    final chatMessageDto = ChatMessageDTO<SendMessageDTO>(
      action: ChatActionEnumerator.sendMessage,
      connectionId: '',
      data: sendMessageData,
    );

    _socketService.sendMessage(_socketService.createMessage(
      EGameAction.chat,
      data: chatMessageDto, // Passa o ChatMessageDTO que é AbsMappable
    ));

    // final tempMessage = ChatMessageEntity(
    //   id: 0, // ID Temporário
    //   senderIdentifier: user.id,
    //   receiverIdentifier: widget.friend.id,
    //   content: _messageController.text,
    //   createdAt: DateTime.now(),
    //   isRead: false,
    // );

    if (mounted) {
      setState(() {
        // _messages.add(tempMessage);
        _messageController.clear();
      });
    }
    _scrollToBottom(); // Esta chamada está correta aqui
    _handleChangeMessageTextFieldBorderRadius();
  }

  void _fetchInitialMessages() {
    if (mounted) {
      setState(() {
        _isLoading = true;
      });
    }
    _chatSocketHandler
        .fetchMessagesBetweenUsers(widget.friend.identifier ?? '');
  }

  void _markUnreadMessagesAsRead() {
    // Verifica se já existe contagem de mensagens não lidas para este amigo
    final currentUnreadCount =
        _chatStore.getUnreadCountForUser(widget.friend.identifier ?? '');

    // Se não há mensagens não lidas registradas, não precisa fazer nada
    if (currentUnreadCount == 0) {
      return;
    }

    // Filtra mensagens não lidas enviadas pelo amigo para o usuário atual
    final unreadMessages = _messages
        .where((message) =>
            message.senderIdentifier == widget.friend.identifier &&
            message.receiverIdentifier == _userStore.state.user.identifier &&
            !message.isRead)
        .toList();

    if (unreadMessages.isNotEmpty) {
      // Extrai os IDs das mensagens não lidas
      final messageIds = unreadMessages.map((message) => message.id).toList();

      // Marca as mensagens como lidas via socket
      _chatSocketHandler.markMessagesAsRead(messageIds);

      debugPrint(
          'Marcando ${messageIds.length} mensagens como lidas para ${widget.friend.name}');
    }

    // Limpa o contador de mensagens não lidas para este amigo no ChatStore
    // (independente de ter mensagens não lidas na lista atual, pois pode haver outras)
    _chatStore.clearUnreadCount(widget.friend.identifier ?? '');
  }

  void handleBack() {
    _chatStore.setCurrentConversationFriendIdentifier('');
    _chatSocketHandler.fetchUnreadCount();
    Modular.to.pop();
  }

  void openMenu() {
    showModalBottomSheet(
      context: context,
      barrierColor: Colors.transparent,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (BuildContext bottomSheetContext) {
        return FriendOptionsBottomSheet(
          doInviteToPlay: _doInviteToPlay,
          doEraseChatHistory: _doEraseChatHistory,
          doUnmatchPlayer: _doUnmatchPlayer,
          doReportPlayer: () =>
              _showMenuReportPlayer(context), // Passa o context da tela
          doBlockPlayer: () =>
              _doBlockPlayer(context), // Passa o context da tela
        );
      },
    );
  }

  void _doInviteToPlay() {
    _chatStore.setCurrentConversationFriendIdentifier('');
    Modular.to.pushReplacementNamed(AppRoutes.gameLobby(), arguments: {
      'friend': widget.friend,
    });
  }

  void _doEraseChatHistory() {
    _chatSocketHandler.eraseHistory(widget.friend.identifier ?? '');
    handleBack();
  }

  void _doUnmatchPlayer() async {
    _controller.removeFriend(int.parse(widget.friend.id)).then((result) {
      if (result) {
        ShowMessage(
            noAvatar: true,
            message: 'The friend was removed successfully',
            onPressed: handleBack);
        return;
      }

      ShowMessage(
          noAvatar: true,
          message:
              'There was a problem removing the friend, please try again later',
          onPressed: handleBack);
    });
  }

  void _showMenuReportPlayer(BuildContext pageContext) {
    ShowDialogFriendMenu(
        user: widget.friend,
        closeOnReportPlayer: true,
        initialScreen: ECurrentFriendMenuState.flagAbusiveContent,
        onSuccessPlayerReport: () =>
            _showSuccessReportUserMessage(pageContext));
  }

  void _showSuccessReportUserMessage(BuildContext pageContext) {
    showDialog(
      context: pageContext,
      barrierDismissible: false,
      builder: (BuildContext dialogContext) {
        return ReportPlayerDialog(
          onBlockPlayer: () {
            _doBlockPlayer(pageContext);
          },
          onReturn: () {},
        );
      },
    );
  }

  void _doReportPlayer(BuildContext pageContext) async {
    final res = await _controller.reportUser(
        int.parse(widget.friend.id), ReportReason.abusiveContent);
    if (res == 1) {
      showDialog(
        context: pageContext,
        barrierDismissible: false,
        builder: (BuildContext dialogContext) {
          return ReportPlayerDialog(
            onBlockPlayer: () {
              _doBlockPlayer(pageContext);
            },
            onReturn: () {},
          );
        },
      );
      return;
    }
    if (res == 0) {
      ShowMessage(
        noAvatar: true,
        message: 'This player has already been reported by you.',
        noButton: true,
        duration: Duration(seconds: 3),
      );
      return;
    }
    ShowMessage(
      noAvatar: true,
      message:
          'There was a problem reporting the player, please try again later',
      noButton: true,
      duration: Duration(seconds: 3),
    );
  }

  void _doBlockPlayer(BuildContext pageContext) async {
    final res = await _controller.blockUser(int.parse(widget.friend.id));
    if (res) {
      showDialog(
        context: pageContext, // Usa o context da página para o showDialog
        barrierDismissible: false,
        builder: (BuildContext dialogContext) {
          return BlockPlayerDialog(
            onDone: () {
              handleBack();
            },
          );
        },
      );
      return;
    }
    ShowMessage(
      noAvatar: true,
      message:
          'There was a problem blocking the player, please try again later',
    );
  }

  String _formatLastOnlineStatus(DateTime? lastaccessAt) {
    if (lastaccessAt == null) {
      return "last online unknown";
    }

    final now = DateTime.now();
    final difference = now.difference(lastaccessAt
        .subtract(Duration(hours: now.timeZoneOffset.inHours.abs())));

    if (difference.inMinutes < 1) {
      return "online now";
    } else if (difference.inMinutes < 60) {
      return "last online ${difference.inMinutes}m ago";
    } else if (difference.inHours < 24) {
      return "last online ${difference.inHours}h ago";
    } else if (difference.inDays < 30) {
      return "last online ${difference.inDays}d ago";
    } else {
      return "last online long ago";
    }
  }

  PreferredSize _buildAppHeader() {
    final friendName = widget.friend.name;
    final friendStatus = _formatLastOnlineStatus(widget.friend.lastaccessAt);
    final friendAvatarUrl = widget.friend.avatarUrl;
    final height = showInfoBanner ? 9.4.h : 11.h;
    return PreferredSize(
      preferredSize: Size.fromHeight(height),
      child: SafeArea(
        child: AppHeader(
          logoSectionLeftWidget: IconButton(
              onPressed: handleBack,
              icon: const Icon(
                QuyckyIcons.arrow_left_circle,
                color: Colors.white,
                size: 23,
              )),
          logoSectionRightWidget: IconButton(
              onPressed: openMenu,
              icon: const Icon(Icons.more_vert, color: Colors.white)),
          centerChild: Container(
            color: Colors.transparent,
            width: 67.w,
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Avatar(
                  imagePath: friendAvatarUrl,
                  size: 6.87.h,
                  addPhotoButton: false,
                  borderColor: Colors.white,
                  borderWidth: 1.5,
                ),
                SizedBox(width: 4.1.w),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      friendName,
                      style: const TextStyle(
                          fontFamily: 'Roboto',
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                          fontSize: 14,
                          letterSpacing: 0.1),
                    ),
                    Text(
                      friendStatus,
                      style: const TextStyle(
                        fontFamily: 'Roboto',
                        color: Colors.white,
                        letterSpacing: -0.2,
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _verifyAndBuildChatBlockedOverlay() {
    if (_isUserSocialLogged) {
      return Container();
    }

    return ClipRRect(
        clipBehavior: Clip.antiAlias,
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 3.0, sigmaY: 3.0),
          child: Container(
              width: 100.w,
              height: 100.h,
              decoration: BoxDecoration(color: Colors.white.withAlpha(85)),
              child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    SizedBox(
                      height: 10.h,
                    ),
                    CustomImage(
                      Assets.svg.padlockWithCircle,
                      width: 10.h,
                      height: 10.h,
                    ),
                    Padding(
                        padding: EdgeInsets.only(top: 3.55.h, bottom: 1.78.h),
                        child: Text(
                          'CHAT LOCKED',
                          style: TextStyle(
                            fontFamily: 'Roboto',
                            fontSize: 16,
                            letterSpacing: 0.1,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        )),
                    Text(
                      'You must sign in with a social account\nto unlock the chat feature',
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        fontFamily: 'Roboto',
                        fontSize: 15,
                        letterSpacing: 0.25,
                        color: Colors.white,
                      ),
                    ),
                    SizedBox(
                      height: 4.6.h,
                    ),
                    SizedBox(
                      width: 83.34.w,
                      child: SocialLoginButtons(
                        callback: () {
                          setTimeout(
                              callback: () => setState(() {}),
                              duration: Duration(seconds: 1));
                        },
                      ),
                    )
                  ])),
        ));
  }

  @override
  Widget build(BuildContext context) {
    return ScopedBuilder<ChatStore, dynamic>(
      store: _chatStore,
      onState: (context, state) {
        final chatListItems = _buildChatListItems();

        // Marca mensagens como lidas quando as mensagens são carregadas
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (_messages.isNotEmpty) {
            _markUnreadMessagesAsRead();
            setTimeout(
              callback: () => _scrollToBottom(),
              duration: Duration(milliseconds: 500),
            );
          }
        });

        return GradientContainer(
          useDefault: true,
          coldOpacity: 0,
          normalOpacity: 0,
          hotOpacity: 0,
          child: Scaffold(
            backgroundColor: Colors.transparent, // Cor de fundo da tela
            appBar: _buildAppHeader(),
            body: Stack(
              children: [
                Column(
                  children: [
                    if (showInfoBanner)
                      _buildInfoBanner(), // Adiciona o banner aqui
                    Expanded(
                      child: ListView.builder(
                        controller: _scrollController,
                        padding: const EdgeInsets.all(16.0),
                        itemCount: chatListItems.length,
                        itemBuilder: (context, index) {
                          return chatListItems[index];
                        },
                      ),
                    ),
                    _buildMessageInputField(),
                  ],
                ),
                _verifyAndBuildChatBlockedOverlay(),
              ],
            ),
          ),
        );
      },
    );
  }

  List<Widget> _buildChatListItems() {
    final List<Widget> items = [];
    bool todaySeparatorShown = false;
    final now = DateTime.now();

    for (int i = 0; i < _messages.length; i++) {
      final message = _messages[i];
      final messageDate = message.createdAt.toLocal();

      // Verifica se a mensagem é de hoje
      final bool isMessageToday = messageDate.year == now.year &&
          messageDate.month == now.month &&
          messageDate.day == now.day;

      if (isMessageToday && !todaySeparatorShown) {
        items.add(_buildDateSeparator("Today"));
        todaySeparatorShown = true;
      }
      // TODO: Adicionar lógica para outros separadores de data (ex: "Yesterday", "dd/MM/yyyy")
      // else if (!isMessageToday) {
      //   // Lógica para mostrar outras datas se necessário, comparando com a data da mensagem anterior
      //   if (i == 0 ||
      //       _messages[i-1].timestamp.year != messageDate.year ||
      //       _messages[i-1].timestamp.month != messageDate.month ||
      //       _messages[i-1].timestamp.day != messageDate.day) {
      //         // items.add(_buildDateSeparator(DateFormat('dd MMM').format(messageDate)));
      //       }
      // }
      items.add(_buildMessageBubble(isMessageToday, message));
    }
    return items;
  }

  Widget _buildInfoBanner() {
    return Container(
      color: CustomColors.primary, // Cor de fundo do banner
      padding: EdgeInsets.symmetric(horizontal: 16.0, vertical: 0.5.h),
      child: Row(
        children: [
          IconButton(
            icon: Icon(QuyckyIcons.close_circle,
                color: Colors.white, size: 2.97.h),
            onPressed: () {
              if (mounted) {
                // Adicionado mounted check
                setState(() {
                  _showInfoBanner = false;
                });
              }
            },
          ),
          const SizedBox(width: 8.0),
          Expanded(
              child: RichText(
            textAlign: TextAlign.center,
            text: TextSpan(
              style: TextStyle(
                color: Colors.white,
                fontSize: 14,
                letterSpacing: 0.2,
                height: 1.2,
                fontFamily: 'Roboto',
              ),
              children: <TextSpan>[
                TextSpan(text: 'Messages will vanish '),
                TextSpan(
                  text: '24 hours\n',
                  style: TextStyle(
                      fontFamily: 'Roboto', fontWeight: FontWeight.bold),
                ),
                TextSpan(text: 'after they are sent. - '),
                TextSpan(
                  text: 'Don’t miss out!',
                  style: TextStyle(
                      fontFamily: 'Roboto', fontWeight: FontWeight.bold),
                ),
              ],
            ),
          )),
        ],
      ),
    );
  }

  void _handleChangeMessageTextFieldBorderRadius() {
    if (mounted) {
      // Adicionado mounted check
      setState(() {
        // _messageTextFieldBorderRadius = // Esta variável é final, não pode ser reatribuída.
        //     _messageController.text.isEmpty ? 122 : 20;
        // A lógica de animação do raio da borda já está no AnimatedContainer,
        // esta função pode ser usada para forçar um rebuild se necessário,
        // mas a mudança do raio em si é baseada no _messageController.text.isEmpty
        // dentro do _buildMessageInputField.
        // Se a intenção era mudar uma variável de estado para o raio, ela não deve ser final.
        // Por ora, vou manter a lógica original do AnimatedContainer que lê _messageController.text.isEmpty.
        // Se _messageTextFieldBorderRadius precisa ser uma variável de estado, ela não deve ser final.
        // Como é final, esta função não tem efeito prático em mudar o raio.
        // Apenas causa um rebuild.
      });
    }
  }

  Widget _buildDateSeparator(String date) {
    return Center(
      child: Container(
        margin: const EdgeInsets.symmetric(vertical: 10.0),
        padding: const EdgeInsets.symmetric(horizontal: 12.0, vertical: 6.0),
        decoration: BoxDecoration(
          color: CustomColors.sunsetOrange,
          borderRadius: BorderRadius.circular(122.0),
        ),
        child: Text(
          date,
          style: const TextStyle(color: Colors.white, fontSize: 12.0),
        ),
      ),
    );
  }

  Widget _buildMessageBubble(bool isMessageToday, ChatMessageEntity message) {
    final bool isMe =
        message.senderIdentifier == _userStore.state.user.identifier;
    final align = isMe ? CrossAxisAlignment.end : CrossAxisAlignment.start;
    final actualBubbleColor =
        isMe ? CustomColors.primary : CustomColors.sunsetOrange;

    final formattedTime = DateFormat('${!isMessageToday ? 'E, ' : ''}HH:mm')
        .format(message.createdAt.toLocal());

    return Column(
      crossAxisAlignment: align,
      children: [
        Container(
          margin: const EdgeInsets.symmetric(vertical: 4.0),
          padding: EdgeInsets.symmetric(horizontal: 16.0, vertical: 10.0),
          decoration: BoxDecoration(
            color: actualBubbleColor,
            borderRadius: BorderRadius.only(
              topLeft: const Radius.circular(20.0),
              topRight: const Radius.circular(20.0),
              bottomLeft: isMe
                  ? const Radius.circular(20.0)
                  : const Radius.circular(0.0),
              bottomRight: isMe
                  ? const Radius.circular(0.0)
                  : const Radius.circular(20.0),
            ),
          ),
          child: Text(
            message.content,
            style: const TextStyle(color: Colors.white, fontSize: 15),
          ),
        ),
        Padding(
          padding: const EdgeInsets.only(top: 2.0, bottom: 8.0),
          child: Text(
            formattedTime,
            style:
                TextStyle(color: Colors.white.withAlpha(178), fontSize: 10.0),
          ),
        )
      ],
    );
  }

  Widget _buildMessageInputField() {
    return Container(
      color: Colors.transparent,
      padding: EdgeInsets.only(left: 6.41.w, right: 6.41.w, bottom: 3.h),
      child: SafeArea(
        child: Row(
          children: [
            Expanded(
              child: AnimatedContainer(
                duration: Duration(milliseconds: 300), // Animação mais rápida
                decoration: BoxDecoration(
                  color: CustomColors.orangeSoda,
                  borderRadius: BorderRadius.circular(
                      _messageController.text.isEmpty
                          ? 122
                          : 20 // Lógica do raio aqui
                      ),
                ),
                child: ConstrainedBox(
                  constraints: BoxConstraints(
                    maxHeight: 21.32.h,
                  ),
                  child: Padding(
                    padding: EdgeInsets.symmetric(
                        vertical: 1.h, horizontal: 1.w), // Ajuste no padding
                    child: TextField(
                      controller: _messageController,
                      style: const TextStyle(color: Colors.white),
                      minLines: 1,
                      maxLines:
                          5, // Limita o número de linhas para evitar expansão excessiva
                      textInputAction: TextInputAction.send,
                      keyboardType: TextInputType.multiline,
                      decoration: InputDecoration(
                        hintText: 'Type something...',
                        hintStyle: TextStyle(
                            color: Colors.white
                                .withOpacity(0.7), // Hint mais suave
                            fontFamily: 'Roboto',
                            fontWeight: FontWeight.w500,
                            fontSize: 14,
                            letterSpacing: 0.2),
                        border: InputBorder.none,
                        contentPadding: const EdgeInsets.symmetric(
                            horizontal: 16.0, // Padding interno
                            vertical: 10.0),
                        suffixIcon: IconButton(
                          // Movido para fora do Padding
                          icon: Icon(
                            QuyckyIcons.send,
                            color: Colors.white,
                            size: 3.2.h,
                          ),
                          onPressed: _sendMessage,
                        ),
                      ),
                      onChanged: (value) {
                        if (mounted) {
                          // Adicionado mounted check
                          setState(() {
                            // Apenas para forçar o rebuild do AnimatedContainer
                          });
                        }
                      },
                      onSubmitted: (_) => _sendMessage(),
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
// FECHAMENTO DA CLASSE _FriendChatState ABAIXO
// A chave extra será removida abaixo
}
