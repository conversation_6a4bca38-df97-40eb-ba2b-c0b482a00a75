import 'package:flutter/material.dart';
import 'package:play_to_vibe/app/features/friendship/domain/entities/friendship_list_item_entity.dart';
import 'package:play_to_vibe/app/theme/colors.dart';
import 'package:play_to_vibe/app/widgets/avatar.dart';
import 'package:play_to_vibe/app/widgets/button.dart';
import 'package:play_to_vibe/core/utils/image_assets.dart';
import 'package:play_to_vibe/core/utils/quycky_icons_icons.dart';
import 'package:sizer/sizer.dart';

class InviteReceivedItem extends StatefulWidget {
  final FriendshipListItem friend;
  final void Function(int id) onAccept;
  final void Function(int id) onCancel;
  const InviteReceivedItem(
      {super.key,
      required this.friend,
      required this.onAccept,
      required this.onCancel});

  @override
  State<InviteReceivedItem> createState() => _InviteReceivedItem();
}

class _InviteReceivedItem extends State<InviteReceivedItem> {
  Widget getButton() => SizedBox(
        width: 23.33.w,
        height: 4.38.h,
        child: Button(
          onPressed: () => widget.onAccept(widget.friend.friendshipId),
          autoSized: true,
          child: Text(
            'ACCEPT',
            style: buttonDefaultTextStyle(Colors.white,
                fontSize: 12, letterSpacing: 1.8),
          ),
        ),
      );
  Widget getIconButton() {
    return IconButton(
        onPressed: () => widget.onCancel(widget.friend.friendshipId),
        icon: const Icon(
          QuyckyIcons.close_circle,
          color: Colors.white,
          size: 25,
        ));
  }

  Widget getWidget() {
    return Container(
      height: 10.66.h,
      constraints: BoxConstraints(maxWidth: 87.18.w),
      decoration: const BoxDecoration(
        color: CustomColors.orangeSoda30,
        borderRadius: BorderRadius.all(Radius.circular(25)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.max,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          SizedBox(
            width: 58.12.w,
            child: Row(
              children: [
                SizedBox(
                  width: 1.w,
                ),
                getIconButton(),
                Padding(
                  padding: EdgeInsets.only(left: 2.8.w, right: 5.38.w),
                  child: Avatar(
                      imagePath: widget.friend.user.avatarUrl,
                      size: 6.87.h,
                      addPhotoButton: false),
                ),
                Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SizedBox(
                      width: 20.w,
                      child: Text(
                        widget.friend.user.name,
                        style: const TextStyle(
                            overflow: TextOverflow.ellipsis,
                            fontFamily: "Roboto",
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                            fontSize: 14),
                      ),
                    ),
                    SizedBox(
                      height: 1.5.h,
                    ),
                    Row(
                      children: [
                        Padding(
                          padding: EdgeInsets.only(right: 1.28.w),
                          child: Image.asset(
                            ImageAssets.fire,
                            height: 1.65.h,
                            width: 3.33.w,
                          ),
                        ),
                        Text(
                          widget.friend.user.pontuation.toString(),
                          style: const TextStyle(
                              fontFamily: "Roboto",
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                              fontSize: 14),
                        ),
                      ],
                    )
                  ],
                ),
              ],
            ),
          ),
          getButton(),
          SizedBox(width: 4.w),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return getWidget();
  }
}
