import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:flutter_triple/flutter_triple.dart';
import 'package:play_to_vibe/app/features/friendship/presenter/store/chat_store.dart';
import 'package:play_to_vibe/app/theme/colors.dart';
import 'package:sizer/sizer.dart';

/// Widget que exibe um resumo das mensagens de chat não lidas
class ChatSummaryWidget extends StatelessWidget {
  final VoidCallback? onTap;

  const ChatSummaryWidget({
    super.key,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final chatStore = Modular.get<ChatStore>();

    return ScopedBuilder<ChatStore, dynamic>(
      store: chatStore,
      onState: (context, state) {
        final totalUnread = chatStore.totalUnreadCount;
        final hasNewMessages = state.hasNewMessages;

        if (!hasNewMessages) {
          return const SizedBox.shrink();
        }

        return GestureDetector(
          onTap: onTap,
          child: Container(
            margin: EdgeInsets.symmetric(horizontal: 4.w, vertical: 1.h),
            padding: EdgeInsets.symmetric(horizontal: 4.w, vertical: 2.h),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  CustomColors.primary.withOpacity(0.8),
                  CustomColors.secondary.withOpacity(0.8),
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Row(
              children: [
                // Ícone de mensagem
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Icon(
                    Icons.chat_bubble,
                    color: Colors.white,
                    size: 24,
                  ),
                ),

                SizedBox(width: 3.w),

                // Texto informativo
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'New Messages',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      SizedBox(height: 0.5.h),
                      Text(
                        _getMessageText(totalUnread),
                        style: TextStyle(
                          color: Colors.white.withOpacity(0.9),
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                ),

                // Badge com contagem
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Text(
                    totalUnread > 99 ? '99+' : totalUnread.toString(),
                    style: TextStyle(
                      color: CustomColors.primary,
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),

                SizedBox(width: 2.w),

                // Seta indicativa
                Icon(
                  Icons.arrow_forward_ios,
                  color: Colors.white.withOpacity(0.8),
                  size: 16,
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  String _getMessageText(int count) {
    if (count == 1) {
      return 'You have 1 unread message';
    } else {
      return 'You have $count unread messages';
    }
  }
}

/// Widget compacto para mostrar apenas o ícone com badge
class ChatIconWithBadge extends StatelessWidget {
  final VoidCallback? onTap;
  final double size;
  final Color? iconColor;

  const ChatIconWithBadge({
    super.key,
    this.onTap,
    this.size = 24,
    this.iconColor,
  });

  @override
  Widget build(BuildContext context) {
    final chatStore = Modular.get<ChatStore>();

    return ScopedBuilder<ChatStore, dynamic>(
      store: chatStore,
      onState: (context, state) {
        final totalUnread = chatStore.totalUnreadCount;

        return GestureDetector(
          onTap: onTap,
          child: Stack(
            children: [
              Icon(
                Icons.chat_bubble_outline,
                size: size,
                color: iconColor ?? Colors.white,
              ),
              if (totalUnread > 0)
                Positioned(
                  right: 0,
                  top: 0,
                  child: Container(
                    padding: const EdgeInsets.all(2),
                    decoration: BoxDecoration(
                      color: CustomColors.error,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.white, width: 1),
                    ),
                    constraints: const BoxConstraints(
                      minWidth: 16,
                      minHeight: 16,
                    ),
                    child: Text(
                      totalUnread > 9 ? '9+' : totalUnread.toString(),
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 10,
                        fontWeight: FontWeight.bold,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ),
            ],
          ),
        );
      },
    );
  }
}
