import 'package:play_to_vibe/app/features/friendship/data/models/friend_match_model.dart';
import 'package:play_to_vibe/app/features/friendship/domain/entities/blocked_player_entity.dart';
import 'package:play_to_vibe/app/features/friendship/data/models/friendship_invite_model.dart';
import 'package:play_to_vibe/app/features/friendship/data/models/friendship_model.dart';
import 'package:play_to_vibe/app/features/friendship/domain/entities/block_player_params_entity.dart'; // Adicionado
import 'package:play_to_vibe/app/features/friendship/domain/entities/report_player_params_entity.dart'; // Adicionado
import 'package:play_to_vibe/app/features/friendship/data/models/friend_profile_model.dart';
import 'package:play_to_vibe/app/features/friendship/domain/request_entities/invite_request.dart';

abstract class IFriendshipDatasource {
  Future<FriendshipInviteModel> inviteUser(InviteRequest data);
  Future<FriendshipInviteModel> orderInvitation(InviteRequest data);
  Future<List<FriendshipModel>> getFriendships(int id);
  Future<FriendProfileModel> getFriendProfile(int id);
  Future<List<FriendshipInviteModel>> getFriendshipInvitationsReceived(int id);
  Future<List<FriendshipInviteModel>> getFriendshipInvitations(int id);
  Future<FriendshipInviteModel> acceptInvite(int id);
  Future<FriendshipInviteModel> rejectInvite(int id);
  Future<FriendMatchModel> checkFriendMatch(String id, bool isNewFriend);
  Future<List<FriendMatchModel>> getFriendMatchHistory();
  Future<bool> removeFriend(int friendId);
  Future<Map<String, dynamic>> generateProfileTempCode();
  Future<Map<String, dynamic>> validateProfileTempCode(String code);
  Future<Map<String, dynamic>> generateFriendlyInvitationTempCode();
  Future<Map<String, dynamic>> validateFriendlyInvitationTempCode(String code);
  Future<bool> blockPlayer(BlockPlayerParamsEntity params);
  Future<bool> unblockPlayer(int blockId);
  Future<List<BlockedPlayerEntity>> getBlockedPlayersList(int userId);
  Future<bool> reportPlayer(ReportPlayerParamsEntity params);
  Future<void> deleteGameInvite(String inviteId);
}
