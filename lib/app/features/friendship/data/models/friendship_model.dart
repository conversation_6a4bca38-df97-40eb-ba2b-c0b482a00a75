import 'dart:convert';

import 'package:play_to_vibe/app/features/friendship/domain/entities/friendship_entity.dart';
import 'package:play_to_vibe/app/features/user/domain/entities/user_entity.dart';

class FriendshipModel extends FriendshipEntity {
  FriendshipModel({
    required id,
    required userId,
    required friendUserId,
    required situation,
    required createdAt,
    required updatedAt,
    required friendUser,
    required user,
  }) : super(
          id: id,
          userId: userId,
          friendUserId: friendUserId,
          situation: situation,
          createdAt: createdAt,
          updatedAt: updatedAt,
          friendUser: friendUser,
          user: user,
        );

  factory FriendshipModel.fromRawJson(String str) =>
      FriendshipModel.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory FriendshipModel.fromJson(Map<String, dynamic> json) {
    dynamic t = FriendshipModel(
      id: json["id"],
      userId: json["user_id"],
      friendUserId: json["friend_user_id"],
      situation: json["situation"],
      createdAt: DateTime.parse(json["created_at"]),
      updatedAt: DateTime.parse(json["updated_at"]),
      friendUser: UserEntity.fromJson(json["friend_user"]),
      user: UserEntity.fromJson(json["user"]),
    );
    return t;
  }

  Map<String, dynamic> toJson() => {
        "id": id,
        "user_id": userId,
        "friend_user_id": friendUserId,
        "situation": situation,
        "created_at": createdAt.toIso8601String(),
        "updated_at": updatedAt.toIso8601String(),
        "friend_user": friendUser.toMap(),
        "user": user.toMap(),
      };

  Map<String, dynamic> toMap() => toJson();
}
