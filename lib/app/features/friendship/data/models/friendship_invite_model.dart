import 'dart:convert';

import 'package:play_to_vibe/app/features/friendship/domain/entities/friendship_invite_entity.dart';
import 'package:play_to_vibe/app/features/user/data/models/user_model.dart';

class FriendshipInviteModel extends FriendshipInviteEntity {
  FriendshipInviteModel({
    id,
    userId,
    invitedUserId,
    situation,
    createdAt,
    updatedAt,
    invitedUser,
    user,
  }) : super(
          id: id,
          userId: userId,
          invitedUserId: invitedUserId,
          situation: situation,
          createdAt: createdAt,
          updatedAt: updatedAt,
          invitedUser: invitedUser,
          user: user,
        );

  factory FriendshipInviteModel.fromRawJson(String str) =>
      FriendshipInviteModel.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory FriendshipInviteModel.fromJson(Map<String, dynamic> json) {
    final res = FriendshipInviteModel(
      id: json["id"] ?? '',
      userId: json["user_id"] ?? '',
      invitedUserId: json["invited_user_id"] ?? '',
      situation: json["situation"] ?? '',
      createdAt: DateTime.parse(json["created_at"]),
      updatedAt: DateTime.parse(json["updated_at"]),
      invitedUser: json["invited_user"] != null
          ? UserModel.fromJson(json["invited_user"])
          : UserModel(
              id: '',
              uuid: '',
              appleId: '',
              googleId: '',
              name: '',
              email: '',
              phoneNumber: '',
              identifier: '',
              onesignalId: '',
              createdAt: DateTime.now(),
              updatedAt: DateTime.now(),
              avatar: '',
              avatarUrl: '',
              isConnected: false,
              pontuation: 0),
      user: json["user"] != null
          ? UserModel.fromJson(json["user"])
          : json["user"],
    );
    return res;
  }

  Map<String, dynamic> toJson() => {
        "id": id,
        "user_id": userId,
        "invited_user_id": invitedUserId,
        "situation": situation,
        "created_at": createdAt.toIso8601String(),
        "updated_at": updatedAt.toIso8601String(),
        "invited_user": invitedUser.toMap(),
        "user": user?.toMap(),
      };
  Map<String, dynamic> toMap() => toJson();
}
