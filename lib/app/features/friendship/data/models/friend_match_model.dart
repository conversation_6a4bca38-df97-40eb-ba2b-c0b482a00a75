import 'dart:convert';

import 'package:play_to_vibe/app/features/friendship/domain/entities/friend_match_entity.dart';
import 'package:play_to_vibe/app/features/user/data/models/tag_model.dart';
import 'package:play_to_vibe/app/features/user/domain/entities/tag_entity.dart';
import 'package:play_to_vibe/app/features/user/domain/entities/user_entity.dart';
import 'package:play_to_vibe/core/utils/type_converters.dart';

class FriendMatchModel extends FriendMatchEntity {
  FriendMatchModel({
    id,
    required matchPercent,
    required commomDimensions,
    required friend,
  }) : super(
          id: id,
          commomDimensions: commomDimensions,
          friend: friend,
          matchPercent: matchPercent,
        );

  factory FriendMatchModel.fromRawJson(String str) =>
      FriendMatchModel.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory FriendMatchModel.fromJson(Map<String, dynamic> json) {
    dynamic t = FriendMatchModel(
      id: dynamicToInt(json["id"]),
      matchPercent: dynamicToDouble(json["match_percent"]),
      commomDimensions: ((json["commom_dimensions"] ?? json["match_tags_obj"])
              as List<dynamic>)
          .map<TagEntity>((x) => TagEntity.fromMap(x as Map<String, dynamic>))
          .toList(),
      friend: UserEntity.fromJson(json["friend"]),
    );
    return t;
  }

  Map<String, dynamic> toJson() => {
        "id": id,
        "match_percent": matchPercent,
        "commom_dimensions": commomDimensions.map((x) => (x).toMap()).toList(),
        "friend": friend.toMap(),
      };

  Map<String, dynamic> toMap() => toJson();
}
