// lib/app/features/friendship/domain/dtos/get_messages_between_users_dto.dart
import 'dart:convert';
import 'package:play_to_vibe/core/entities/abs_mappable.dart';

class EraseHistoryDTO implements AbsMappable {
  final String friendIdentifier;

  EraseHistoryDTO({
    required this.friendIdentifier,
  });

  @override
  Map<String, dynamic> toMap() {
    final map = <String, dynamic>{
      'friendIdentifier': friendIdentifier,
    };
    return map;
  }

  factory EraseHistoryDTO.fromMap(Map<String, dynamic> map) {
    return EraseHistoryDTO(
      friendIdentifier: map['friendIdentifier'] as String,
    );
  }

  @override
  String toJson() => json.encode(toMap());

  factory EraseHistoryDTO.fromJson(String source) =>
      EraseHistoryDTO.fromMap(json.decode(source) as Map<String, dynamic>);
}
