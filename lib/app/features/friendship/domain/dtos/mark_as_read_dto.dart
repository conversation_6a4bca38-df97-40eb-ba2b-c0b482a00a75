// lib/app/features/friendship/domain/dtos/mark_as_read_dto.dart
import 'dart:convert';
import 'package:play_to_vibe/core/entities/abs_mappable.dart';

class MarkAsReadDTO implements AbsMappable {
  final List<int> messageIds;

  MarkAsReadDTO({
    required this.messageIds,
  });

  @override
  Map<String, dynamic> toMap() {
    return {
      'messageIds': messageIds,
    };
  }

  factory MarkAsReadDTO.fromMap(Map<String, dynamic> map) {
    return MarkAsReadDTO(
      messageIds: List<int>.from(map['messageIds'] as List),
    );
  }

  @override
  String toJson() => json.encode(toMap());

  factory MarkAsReadDTO.fromJson(String source) =>
      MarkAsReadDTO.fromMap(json.decode(source) as Map<String, dynamic>);
}
