// lib/app/features/friendship/domain/dtos/get_messages_dto.dart
import 'dart:convert';
import 'package:play_to_vibe/core/entities/abs_mappable.dart';

class GetMessagesDTO implements AbsMappable {
  final int? page;
  final int? limit;

  GetMessagesDTO({
    this.page,
    this.limit,
  });

  @override
  Map<String, dynamic> toMap() {
    final map = <String, dynamic>{};
    if (page != null) {
      map['page'] = page;
    }
    if (limit != null) {
      map['limit'] = limit;
    }
    return map;
  }

  factory GetMessagesDTO.fromMap(Map<String, dynamic> map) {
    return GetMessagesDTO(
      page: map['page'] as int?,
      limit: map['limit'] as int?,
    );
  }

  @override
  String toJson() => json.encode(toMap());

  factory GetMessagesDTO.fromJson(String source) =>
      GetMessagesDTO.fromMap(json.decode(source) as Map<String, dynamic>);
}
