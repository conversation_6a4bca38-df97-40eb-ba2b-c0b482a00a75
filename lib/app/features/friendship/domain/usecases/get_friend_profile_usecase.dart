// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:dartz/dartz.dart';
import 'package:play_to_vibe/app/features/friendship/domain/entities/friendship_profile_entity.dart';
import 'package:play_to_vibe/app/features/friendship/domain/repositories/friendship_repository.dart';

import 'package:play_to_vibe/core/usecase/errors/failure.dart';
import 'package:play_to_vibe/core/usecase/usecase.dart';

class GetFriendProfileUseCase implements UseCase<FriendProfileEntity, int> {
  final IFriendshipRepository repository;

  GetFriendProfileUseCase(
    this.repository,
  );

  @override
  Future<Either<Failure, FriendProfileEntity>> call(int id) async {
    return await repository.getFriendProfile(id);
  }
}
