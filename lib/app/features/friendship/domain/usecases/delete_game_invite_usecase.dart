import 'package:dartz/dartz.dart';
import 'package:play_to_vibe/app/features/friendship/domain/repositories/friendship_repository.dart';
import 'package:play_to_vibe/core/usecase/errors/failure.dart';

class DeleteGameInviteUseCase {
  final IFriendshipRepository repository;

  DeleteGameInviteUseCase(this.repository);

  Future<Either<Failure, void>> call(String inviteId) async {
    return await repository.deleteGameInvite(inviteId);
  }
}
