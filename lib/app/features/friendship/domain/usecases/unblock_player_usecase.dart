import 'package:dartz/dartz.dart';
import 'package:play_to_vibe/app/features/friendship/domain/repositories/friendship_repository.dart';
import 'package:play_to_vibe/core/usecase/errors/failure.dart';
import 'package:play_to_vibe/core/usecase/usecase.dart';

class UnblockPlayerUsecase implements UseCase<bool, int> {
  final IFriendshipRepository repository;

  UnblockPlayerUsecase(this.repository);

  @override
  Future<Either<Failure, bool>> call(int blockId) async {
    return await repository.unblockPlayer(blockId);
  }
}
