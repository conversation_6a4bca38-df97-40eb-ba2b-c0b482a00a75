// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:dartz/dartz.dart';
import 'package:play_to_vibe/app/features/friendship/domain/repositories/friendship_repository.dart';

import 'package:play_to_vibe/core/usecase/errors/failure.dart';
import 'package:play_to_vibe/core/usecase/usecase.dart';

class RemoveFriendUsecase implements UseCase<bool, int> {
  final IFriendshipRepository repository;

  RemoveFriendUsecase(
    this.repository,
  );

  @override
  Future<Either<Failure, bool>> call(int friendId) async {
    return await repository.removeFriend(friendId);
  }
}
