// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:dartz/dartz.dart';
import 'package:play_to_vibe/app/features/friendship/domain/entities/invitation_ticket_entity.dart';
import 'package:play_to_vibe/app/features/friendship/domain/repositories/friendship_repository.dart';

import 'package:play_to_vibe/core/usecase/errors/failure.dart';
import 'package:play_to_vibe/core/usecase/usecase.dart';

class GenerateFriendlyInvitationTempCodeUsecase
    implements UseCase<InvitationTicketEntity, NoParams> {
  final IFriendshipRepository repository;

  GenerateFriendlyInvitationTempCodeUsecase(
    this.repository,
  );

  @override
  Future<Either<Failure, InvitationTicketEntity>> call(
      NoParams noParams) async {
    return await repository.generateFriendlyInvitationTempCode();
  }
}
