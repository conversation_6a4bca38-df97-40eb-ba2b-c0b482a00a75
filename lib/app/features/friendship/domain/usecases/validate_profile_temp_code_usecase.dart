// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:dartz/dartz.dart';
import 'package:play_to_vibe/app/features/friendship/domain/entities/profile_temp_code_entity.dart';
import 'package:play_to_vibe/app/features/friendship/domain/repositories/friendship_repository.dart';

import 'package:play_to_vibe/core/usecase/errors/failure.dart';
import 'package:play_to_vibe/core/usecase/usecase.dart';

class ValidateProfileTempCodeUsecase
    implements UseCase<ProfileTempCodeEntity, String> {
  final IFriendshipRepository repository;

  ValidateProfileTempCodeUsecase(
    this.repository,
  );

  @override
  Future<Either<Failure, ProfileTempCodeEntity>> call(String code) async {
    return await repository.validateProfileTempCode(code);
  }
}
