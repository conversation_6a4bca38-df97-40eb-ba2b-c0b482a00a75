// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:dartz/dartz.dart';
import 'package:play_to_vibe/app/features/friendship/domain/entities/friendship_invite_entity.dart';
import 'package:play_to_vibe/app/features/friendship/domain/repositories/friendship_repository.dart';
import 'package:play_to_vibe/app/features/friendship/domain/request_entities/invite_request.dart';

import 'package:play_to_vibe/core/usecase/errors/failure.dart';
import 'package:play_to_vibe/core/usecase/usecase.dart';

class AddFriendUseCase
    implements UseCase<FriendshipInviteEntity, InviteRequest> {
  final IFriendshipRepository repository;

  AddFriendUseCase(
    this.repository,
  );

  @override
  Future<Either<Failure, FriendshipInviteEntity>> call(
      InviteRequest params) async {
    return await repository.inviteUser(params);
  }
}
