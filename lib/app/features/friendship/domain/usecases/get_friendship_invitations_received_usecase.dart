// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:dartz/dartz.dart';
import 'package:play_to_vibe/app/features/friendship/domain/entities/friendship_invite_entity.dart';
import 'package:play_to_vibe/app/features/friendship/domain/repositories/friendship_repository.dart';

import 'package:play_to_vibe/core/usecase/errors/failure.dart';
import 'package:play_to_vibe/core/usecase/usecase.dart';

class GetFriendshipInvitationsReceivedUseCase
    implements UseCase<List<FriendshipInviteEntity>, int> {
  final IFriendshipRepository repository;

  GetFriendshipInvitationsReceivedUseCase(
    this.repository,
  );

  @override
  Future<Either<Failure, List<FriendshipInviteEntity>>> call(int id) async {
    return await repository.getFriendshipInvitationsReceived(id);
  }
}
