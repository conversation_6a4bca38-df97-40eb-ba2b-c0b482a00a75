// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:play_to_vibe/app/features/friendship/domain/entities/friendship_entity.dart';
import 'package:play_to_vibe/app/features/friendship/domain/entities/friendship_invite_entity.dart';
import 'package:play_to_vibe/app/features/friendship/domain/entities/friendship_list_item_entity.dart';
import 'package:play_to_vibe/app/features/friendship/domain/entities/friendship_profile_entity.dart';
import 'package:play_to_vibe/app/features/friendship/domain/entities/game_invite_entity.dart';

class FriendshipStoreEntity {
  final FriendProfileEntity? friendProfile;
  final List<FriendshipListItem> friendshipListItems;
  final List<FriendshipListItem> friendshipInviteReceivedListItems;
  final List<FriendshipListItem> friendshipInviteSentListItems;
  final List<FriendshipEntity> friendships;
  final List<FriendshipInviteEntity> invites;
  final List<FriendshipInviteEntity> invitesReceived;
  final List<GameInviteEntity> gameInvites;

  const FriendshipStoreEntity({
    this.friendProfile,
    required this.friendshipListItems,
    required this.friendshipInviteReceivedListItems,
    required this.friendshipInviteSentListItems,
    required this.friendships,
    required this.invites,
    required this.invitesReceived,
    required this.gameInvites,
  });

  FriendshipStoreEntity copyWith(
      {FriendProfileEntity? friendProfile,
      List<FriendshipListItem>? friendshipListItems,
      List<FriendshipEntity>? friendships,
      List<FriendshipInviteEntity>? invites,
      List<FriendshipInviteEntity>? invitesReceived,
      List<FriendshipListItem>? friendshipInviteReceivedListItems,
      List<FriendshipListItem>? friendshipInviteSentListItems,
      List<GameInviteEntity>? gameInvites}) {
    return FriendshipStoreEntity(
      friendProfile: friendProfile ?? this.friendProfile,
      friendshipListItems: friendshipListItems ?? this.friendshipListItems,
      friendshipInviteReceivedListItems: friendshipInviteReceivedListItems ??
          this.friendshipInviteReceivedListItems,
      friendshipInviteSentListItems:
          friendshipInviteSentListItems ?? this.friendshipInviteSentListItems,
      friendships: friendships ?? this.friendships,
      invites: invites ?? this.invites,
      invitesReceived: invitesReceived ?? this.invitesReceived,
      gameInvites: gameInvites ?? this.gameInvites,
    );
  }
}
