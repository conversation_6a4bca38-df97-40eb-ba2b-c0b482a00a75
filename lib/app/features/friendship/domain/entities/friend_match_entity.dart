import 'package:play_to_vibe/app/features/user/domain/entities/tag_entity.dart';
import 'package:play_to_vibe/app/features/user/domain/entities/user_entity.dart';

class FriendMatchEntity {
  final int id;
  final double matchPercent;
  final List<TagEntity> commomDimensions;
  final UserEntity friend;

  FriendMatchEntity({
    this.id = 0,
    required this.matchPercent,
    required this.commomDimensions,
    required this.friend,
  });

  FriendMatchEntity copyWith({
    int? id,
    double? matchPercent,
    List<TagEntity>? commomDimensions,
    UserEntity? friend,
  }) =>
      FriendMatchEntity(
        id: id ?? this.id,
        matchPercent: matchPercent ?? this.matchPercent,
        commomDimensions: commomDimensions ?? this.commomDimensions,
        friend: friend ?? this.friend,
      );

  @override
  toString() {
    return 'id: $id, FriendMatchEntity(matchPercent: $matchPercent, commomDimensions: $commomDimensions, friend: $friend)';
  }
}
