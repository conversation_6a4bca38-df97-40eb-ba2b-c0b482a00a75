// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:play_to_vibe/app/features/user/domain/entities/user_entity.dart';

enum EFriendshipListItem { inviteSent, inviteReceived, friendship }

class FriendshipListItem {
  final int friendshipId;
  final UserEntity user;
  final EFriendshipListItem type;

  FriendshipListItem({
    required this.friendshipId,
    required this.user,
    required this.type,
  });
}
