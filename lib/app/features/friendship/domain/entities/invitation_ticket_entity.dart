import 'dart:convert';

import 'package:play_to_vibe/core/utils/type_converters.dart';

class InvitationTicketEntity {
  final String id;
  final String userId;
  final String code;
  final String status;
  final DateTime createdAt;
  final String message;

  InvitationTicketEntity({
    required this.id,
    required this.userId,
    required this.code,
    required this.status,
    required this.createdAt,
    required this.message,
  });

  InvitationTicketEntity copyWith({
    String? id,
    String? userId,
    String? code,
    String? status,
    DateTime? createdAt,
    String? message,
  }) =>
      InvitationTicketEntity(
        id: id ?? this.id,
        userId: userId ?? this.userId,
        code: code ?? this.code,
        status: status ?? this.status,
        createdAt: createdAt ?? this.createdAt,
        message: message ?? this.message,
      );

  factory InvitationTicketEntity.fromRawJson(String str) =>
      InvitationTicketEntity.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory InvitationTicketEntity.fromJson(Map<String, dynamic> json) =>
      InvitationTicketEntity(
        id: dynamicToString(json["id"]),
        userId: dynamicToString(json["user_id"]),
        code: dynamicToString(json["code"]),
        status: dynamicToString(json["status"]),
        createdAt: DateTime.parse(json["created_at"]),
        message: dynamicToString(json["message"]),
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "user_id": userId,
        "code": code,
        "status": status,
        "created_at": createdAt.toIso8601String(),
        "message": message,
      };
}
