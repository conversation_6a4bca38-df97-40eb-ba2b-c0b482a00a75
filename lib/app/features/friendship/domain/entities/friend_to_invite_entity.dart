import 'package:play_to_vibe/app/features/user/domain/entities/user_entity.dart';

class FriendToInviteEntity {
  FriendToInviteEntity({
    required this.friend,
    required this.pontuation,
    required this.isConnected,
  });

  final UserEntity friend;
  final int pontuation;
  final bool isConnected;

  FriendToInviteEntity copyWith(
          {UserEntity? friend, int? pontuation, bool? isConnected}) =>
      FriendToInviteEntity(
        friend: friend ?? this.friend,
        pontuation: pontuation ?? this.pontuation,
        isConnected: isConnected ?? this.isConnected,
      );
}
