import 'package:play_to_vibe/app/features/user/domain/entities/user_entity.dart';

class FriendshipInviteEntity {
  final String id;
  final String userId;
  final String invitedUserId;
  final String situation;
  final DateTime createdAt;
  final DateTime updatedAt;
  final UserEntity invitedUser;
  final UserEntity? user;

  FriendshipInviteEntity({
    required this.id,
    required this.userId,
    required this.invitedUserId,
    required this.situation,
    required this.createdAt,
    required this.updatedAt,
    required this.invitedUser,
    this.user,
  });

  FriendshipInviteEntity copyWith({
    String? id,
    String? userId,
    String? invitedUserId,
    String? situation,
    DateTime? createdAt,
    DateTime? updatedAt,
    UserEntity? invitedUser,
    UserEntity? user,
  }) =>
      FriendshipInviteEntity(
        id: id ?? this.id,
        userId: userId ?? this.userId,
        invitedUserId: invitedUserId ?? this.invitedUserId,
        situation: situation ?? this.situation,
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
        invitedUser: invitedUser ?? this.invitedUser,
        user: user ?? this.user,
      );
}
