import 'package:play_to_vibe/core/utils/type_converters.dart';
import 'package:play_to_vibe/app/features/user/domain/entities/user_entity.dart';

class ChatMessageEntity {
  final int id;
  final String senderIdentifier;
  final String receiverIdentifier;
  final String content;
  final DateTime createdAt;
  final bool isRead;
  final UserEntity? sender;
  final UserEntity? receiver;

  ChatMessageEntity({
    required this.id,
    required this.senderIdentifier,
    required this.receiverIdentifier,
    required this.content,
    required this.createdAt,
    this.isRead = false,
    this.sender,
    this.receiver,
  });

  factory ChatMessageEntity.fromMap(Map<String, dynamic> map) {
    return ChatMessageEntity(
      id: dynamicToInt(map['id']),
      senderIdentifier: dynamicToString(map['sender_identifier']),
      receiverIdentifier: dynamicToString(map['receiver_identifier']),
      content: dynamicToString(map['content']),
      createdAt: dynamicToDateTime(map['created_at']),
      isRead: dynamicToBoolean(map['is_read']),
      sender: map['sender'] != null ? UserEntity.fromJson(map['sender']) : null,
      receiver:
          map['receiver'] != null ? UserEntity.fromJson(map['receiver']) : null,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'sender_identifier': senderIdentifier,
      'receiver_identifier': receiverIdentifier,
      'content': content,
      'created_at': createdAt,
      'is_read': isRead,
      'sender': sender != null
          ? {
              'id': int.parse(sender!.id),
              'identifier': sender!.identifier,
              'name': sender!.name,
              'avatar': sender!.avatar,
              'lastaccess_at': sender!.lastaccessAt,
            }
          : null,
      'receiver': receiver != null
          ? {
              'id': int.parse(receiver!.id),
              'identifier': receiver!.identifier,
              'name': receiver!.name,
              'avatar': receiver!.avatar,
              'lastaccess_at': receiver!.lastaccessAt,
            }
          : null,
    };
  }

  ChatMessageEntity copyWith({
    int? id,
    String? senderIdentifier,
    String? receiverIdentifier,
    String? content,
    DateTime? createdAt,
    bool? isRead,
    UserEntity? sender,
    UserEntity? receiver,
  }) {
    return ChatMessageEntity(
      id: id ?? this.id,
      senderIdentifier: senderIdentifier ?? this.senderIdentifier,
      receiverIdentifier: receiverIdentifier ?? this.receiverIdentifier,
      content: content ?? this.content,
      createdAt: createdAt ?? this.createdAt,
      isRead: isRead ?? this.isRead,
      sender: sender ?? this.sender,
      receiver: receiver ?? this.receiver,
    );
  }
}
