import 'package:equatable/equatable.dart';
import 'package:play_to_vibe/app/features/friendship/domain/entities/chat_message_entity.dart';

class ChatStoreEntity extends Equatable {
  final Map<String, int> unreadMessagesCount;
  final String currentConversationFriendIdentifier;
  final List<ChatMessageEntity> currentMessages;
  final String localUserIdentifier;

  const ChatStoreEntity({
    this.unreadMessagesCount = const {},
    this.currentConversationFriendIdentifier = '',
    this.currentMessages = const [],
    this.localUserIdentifier = '',
  });

  ChatStoreEntity copyWith(
      {Map<String, int>? unreadMessagesCount,
      String? currentConversationFriendIdentifier,
      List<ChatMessageEntity>? currentMessages,
      bool? hasNewMessages,
      String? localUserIdentifier}) {
    return ChatStoreEntity(
      unreadMessagesCount: unreadMessagesCount ?? this.unreadMessagesCount,
      currentMessages: currentMessages ?? this.currentMessages,
      localUserIdentifier: localUserIdentifier ?? this.localUserIdentifier,
      currentConversationFriendIdentifier:
          currentConversationFriendIdentifier ??
              this.currentConversationFriendIdentifier,
    );
  }

  @override
  List<Object> get props => [
        unreadMessagesCount,
        currentMessages,
        localUserIdentifier,
        currentConversationFriendIdentifier
      ];
}
