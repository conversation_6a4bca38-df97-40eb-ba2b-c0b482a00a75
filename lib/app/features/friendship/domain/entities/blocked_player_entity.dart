import 'package:equatable/equatable.dart';
import 'package:play_to_vibe/core/utils/type_converters.dart';
import './blocked_user_details_entity.dart';

class BlockedPlayerEntity extends Equatable {
  final int id;
  final String userId;
  final String blockedUserId;
  final DateTime createdAt;
  final DateTime updatedAt;
  final BlockedUserDetailsEntity blockedUser;

  const BlockedPlayerEntity({
    required this.id,
    required this.userId,
    required this.blockedUserId,
    required this.createdAt,
    required this.updatedAt,
    required this.blockedUser,
  });

  factory BlockedPlayerEntity.fromJson(Map<String, dynamic> json) {
    return BlockedPlayerEntity(
      id: dynamicToInt(json['id']),
      userId: json['user_id'] as String,
      blockedUserId: json['blocked_user_id'] as String,
      createdAt: dynamicToDateTime(json['created_at']),
      updatedAt: dynamicToDateTime(json['updated_at']),
      blockedUser: BlockedUserDetailsEntity.fromJson(
          json['blocked_user'] != null
              ? json['blocked_user'] as Map<String, dynamic>
              : null),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'blocked_user_id': blockedUserId,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'blocked_user': blockedUser.toJson(),
    };
  }

  @override
  List<Object?> get props => [
        id,
        userId,
        blockedUserId,
        createdAt,
        updatedAt,
        blockedUser,
      ];
}
