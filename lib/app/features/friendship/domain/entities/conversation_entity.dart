import 'package:play_to_vibe/app/features/friendship/domain/entities/chat_message_entity.dart';
import 'package:play_to_vibe/core/utils/type_converters.dart';

class ConversationEntity {
  final String userIdentifier;
  final String userName;
  final String? userAvatar;
  final ChatMessageEntity lastMessage;
  final int unreadCount;

  ConversationEntity({
    required this.userIdentifier,
    required this.userName,
    this.userAvatar,
    required this.lastMessage,
    required this.unreadCount,
  });

  factory ConversationEntity.fromMap(Map<String, dynamic> map) {
    return ConversationEntity(
      userIdentifier: dynamicToString(map['userIdentifier']),
      userName: dynamicToString(map['userName']),
      userAvatar:
          map['userAvatar'] != null ? dynamicToString(map['userAvatar']) : null,
      lastMessage:
          ChatMessageEntity.fromMap(map['lastMessage'] as Map<String, dynamic>),
      unreadCount: dynamicToInt(map['unreadCount']),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'userIdentifier': userIdentifier,
      'userName': userName,
      'userAvatar': userAvatar,
      'lastMessage': lastMessage.toMap(),
      'unreadCount': unreadCount,
    };
  }

  ConversationEntity copyWith({
    String? userIdentifier,
    String? userName,
    String? userAvatar,
    ChatMessageEntity? lastMessage,
    int? unreadCount,
  }) {
    return ConversationEntity(
      userIdentifier: userIdentifier ?? this.userIdentifier,
      userName: userName ?? this.userName,
      userAvatar: userAvatar ?? this.userAvatar,
      lastMessage: lastMessage ?? this.lastMessage,
      unreadCount: unreadCount ?? this.unreadCount,
    );
  }
}
