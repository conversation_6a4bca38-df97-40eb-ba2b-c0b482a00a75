// ignore_for_file: public_member_api_docs, sort_constructors_first

import 'dart:convert';

import 'package:play_to_vibe/app/features/user/domain/entities/user_entity.dart';

class FriendProfileEntity {
  FriendProfileEntity({
    required this.user,
    required this.answers,
  });

  final UserEntity user;
  final List<FriendshipAnswerEntity> answers;

  FriendProfileEntity copyWith({
    UserEntity? user,
    List<FriendshipAnswerEntity>? answers,
  }) =>
      FriendProfileEntity(
        user: user ?? this.user,
        answers: answers ?? this.answers,
      );

  Map<String, dynamic> toMap() {
    // TODO: implement toString
    return <String, dynamic>{
      'user': user.toRawJson(),
      'answers': answers.map((element) => element.toMap()).toList(),
    };
  }

  String toRawJson() => json.encode(toMap());
}

class FriendshipAnswerEntity {
  FriendshipAnswerEntity({
    required this.id,
    required this.userId,
    required this.roomId,
    required this.questionId,
    required this.value,
    required this.createdAt,
    required this.updatedAt,
    required this.room,
    required this.question,
    required this.points,
  });

  final String id;
  final String userId;
  final String roomId;
  final String questionId;
  final String value;
  final DateTime createdAt;
  final DateTime updatedAt;
  final FriendshipRoomEntity room;
  final FriendshipQuestionEntity question;
  final List<FriendshipPointEntity> points;

  FriendshipAnswerEntity copyWith({
    String? id,
    String? userId,
    String? roomId,
    String? questionId,
    String? value,
    DateTime? createdAt,
    DateTime? updatedAt,
    FriendshipRoomEntity? room,
    FriendshipQuestionEntity? question,
    List<FriendshipPointEntity>? points,
  }) =>
      FriendshipAnswerEntity(
        id: id ?? this.id,
        userId: userId ?? this.userId,
        roomId: roomId ?? this.roomId,
        questionId: questionId ?? this.questionId,
        value: value ?? this.value,
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
        room: room ?? this.room,
        question: question ?? this.question,
        points: points ?? this.points,
      );

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'userId': userId,
      'roomId': roomId,
      'questionId': questionId,
      'value': value,
      'createdAt': createdAt,
      'updatedAt': updatedAt,
      'room': room,
      'question': question,
      'points': points.map((element) => element.toMap()).toList()
    };
  }
}

class FriendshipQuestionEntity {
  FriendshipQuestionEntity({
    required this.id,
    required this.description,
    required this.isActived,
    required this.stage,
    required this.createdAt,
    required this.updatedAt,
  });

  final String id;
  final String description;
  final int isActived;
  final int stage;
  final DateTime createdAt;
  final DateTime updatedAt;

  FriendshipQuestionEntity copyWith({
    String? id,
    String? description,
    int? isActived,
    int? stage,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) =>
      FriendshipQuestionEntity(
        id: id ?? this.id,
        description: description ?? this.description,
        isActived: isActived ?? this.isActived,
        stage: stage ?? this.stage,
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
      );
}

class FriendshipRoomEntity {
  FriendshipRoomEntity({
    required this.id,
    required this.name,
    required this.howManyPeople,
    required this.isBloqued,
    required this.questionTimeInSeconds,
    required this.responseTimeInSeconds,
    required this.createdAt,
    required this.updatedAt,
  });

  final String id;
  final String name;
  final int howManyPeople;
  final bool isBloqued;
  final int questionTimeInSeconds;
  final int responseTimeInSeconds;
  final DateTime createdAt;
  final DateTime updatedAt;

  FriendshipRoomEntity copyWith({
    String? id,
    String? name,
    int? howManyPeople,
    bool? isBloqued,
    int? questionTimeInSeconds,
    int? responseTimeInSeconds,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) =>
      FriendshipRoomEntity(
        id: id ?? this.id,
        name: name ?? this.name,
        howManyPeople: howManyPeople ?? this.howManyPeople,
        isBloqued: isBloqued ?? this.isBloqued,
        questionTimeInSeconds:
            questionTimeInSeconds ?? this.questionTimeInSeconds,
        responseTimeInSeconds:
            responseTimeInSeconds ?? this.responseTimeInSeconds,
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
      );
}

class FriendshipPointEntity {
  final int id;
  final int userId;
  final int roomId;
  final int questionId;
  final int levelId;
  final int answerId;
  final int pointTypeId;
  final int userGiveId;
  final DateTime createdAt;
  final DateTime updatedAt;
  FriendshipPointEntity({
    required this.id,
    required this.userId,
    required this.roomId,
    required this.questionId,
    required this.levelId,
    required this.answerId,
    required this.pointTypeId,
    required this.userGiveId,
    required this.createdAt,
    required this.updatedAt,
  });

  FriendshipPointEntity copyWith({
    int? id,
    int? userId,
    int? roomId,
    int? questionId,
    int? levelId,
    int? answerId,
    int? pointTypeId,
    int? userGiveId,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return FriendshipPointEntity(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      roomId: roomId ?? this.roomId,
      questionId: questionId ?? this.questionId,
      levelId: levelId ?? this.levelId,
      answerId: answerId ?? this.answerId,
      pointTypeId: pointTypeId ?? this.pointTypeId,
      userGiveId: userGiveId ?? this.userGiveId,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'userId': userId,
      'roomId': roomId,
      'questionId': questionId,
      'levelId': levelId,
      'answerId': answerId,
      'pointTypeId': pointTypeId,
      'userGiveId': userGiveId,
      'createdAt': createdAt,
      'updatedAt': updatedAt
    };
  }
}
