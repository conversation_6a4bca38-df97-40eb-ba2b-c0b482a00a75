import 'package:play_to_vibe/app/features/user/domain/entities/user_entity.dart';

class GameInviteEntity {
  final String id;
  final String gameId;
  final String roomName;
  final String situation;
  final DateTime createdAt;
  final DateTime expiresAt;
  final UserEntity player;

  GameInviteEntity({
    required this.id,
    required this.gameId,
    required this.roomName,
    required this.situation,
    required this.createdAt,
    required this.expiresAt,
    required this.player,
  });

  GameInviteEntity copyWith({
    String? id,
    String? gameId,
    String? roomName,
    String? situation,
    DateTime? createdAt,
    DateTime? expiresAt,
    UserEntity? player,
  }) =>
      GameInviteEntity(
        id: id ?? this.id,
        gameId: gameId ?? this.gameId,
        roomName: roomName ?? this.roomName,
        situation: situation ?? this.situation,
        createdAt: createdAt ?? this.createdAt,
        expiresAt: expiresAt ?? this.expiresAt,
        player: player ?? this.player,
      );

  factory GameInviteEntity.fromJson(Map<String, dynamic> json) =>
      GameInviteEntity(
        id: json["id"].toString(),
        gameId: json["game_id"].toString(),
        roomName: json["room_name"],
        situation: json["situation"],
        createdAt: DateTime.parse(json["created_at"]),
        expiresAt: DateTime.parse(json["expires_at"]),
        player: UserEntity.fromJson(json["player"]),
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "game_id": gameId,
        "room_name": roomName,
        "situation": situation,
        "created_at": createdAt.toIso8601String(),
        "updated_at": expiresAt.toIso8601String(),
        "player": player.toMap(),
      };
}
