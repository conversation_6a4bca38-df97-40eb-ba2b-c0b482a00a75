import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:flutter_triple/flutter_triple.dart';
import 'package:play_to_vibe/app/features/home/<USER>/store/promotion_countdown_store.dart';
import 'package:play_to_vibe/app/features/home/<USER>/store/promotion_store.dart';
import 'package:play_to_vibe/core/entities/promotion_entity.dart';
import 'package:rive/rive.dart';

class PromotionGiftNotice extends StatefulWidget {
  final void Function()? onPressed;
  final Color textColor;
  final double fontSize;
  final double letterSpacing;
  final double imageHeight;
  final double imageWidth;
  final double textTopPadding;
  final PromotionEntity promotion;
  final bool showLabelEndTime;

  const PromotionGiftNotice(
      {super.key,
      required this.promotion,
      this.onPressed,
      this.textColor = Colors.black,
      this.fontSize = 15,
      this.letterSpacing = 2,
      this.imageHeight = 150,
      this.textTopPadding = 16.0,
      this.imageWidth = 220,
      this.showLabelEndTime = false});

  @override
  State<PromotionGiftNotice> createState() => _PromotionGiftNotice();
}

class _PromotionGiftNotice extends State<PromotionGiftNotice> {
  late RiveAnimationController _riveAnimationController;
  Timer? countdownTimer;
  final _promotionStore = Modular.get<PromotionStore>();
  final _promotionCountdownStore = Modular.get<PromotionCountdownStore>();

  @override
  void initState() {
    super.initState();
    startTimer();
    _riveAnimationController = SimpleAnimation(
      'shake',
    );
  }

  void closePromotion() {
    _promotionStore.changePromotionData(widget.promotion.copyWith(
      status: 'expired',
    ));
    // _promotionStore.removePromotion(widget.promotion.identifier);
  }

  void startTimer() {
    if (widget.promotion.minutesToClose == 0) {
      return;
    }
    final timeDifference =
        DateTime.now().difference(widget.promotion.startTime);
    if (timeDifference.inMinutes >= widget.promotion.minutesToClose) {
      closePromotion();
      return;
    }
    int remainderSeconds =
        (widget.promotion.minutesToClose * 60) - timeDifference.inSeconds;
    _promotionCountdownStore.setTotalTime(Duration(seconds: remainderSeconds));
    countdownTimer =
        Timer.periodic(const Duration(seconds: 1), (_) => setCountDown());
  }

  void stopTimer() {
    countdownTimer!.cancel();
  }

  void resetTimer() {
    stopTimer();
    _promotionCountdownStore.setTotalTime(const Duration(seconds: 0));
  }

  void setCountDown() {
    final seconds = _promotionCountdownStore.state.inSeconds - 1;
    if (seconds == 0) {
      countdownTimer!.cancel();
      closePromotion();
    }
    _promotionCountdownStore.setTotalTime(Duration(seconds: seconds));
  }

  void onPressed() {
    if (widget.onPressed != null) {
      widget.onPressed!();
    }
  }

  String formattedDigits(int n) => n.toString().padLeft(2, '0');

  String getCurrentTime(Duration currentState) {
    final minutes = formattedDigits(currentState.inMinutes.remainder(60));
    final seconds = formattedDigits(currentState.inSeconds.remainder(60));
    return '$minutes:$seconds';
  }

  Widget _buildTimer(Duration currentDuration) {
    if (widget.promotion.minutesToClose == 0) {
      return Container();
    }
    return Text(
      getCurrentTime(currentDuration),
      style: TextStyle(
          fontFamily: 'Roboto',
          fontWeight: FontWeight.w700,
          fontSize: widget.fontSize,
          letterSpacing: widget.letterSpacing,
          color: widget.textColor,
          decoration: TextDecoration.none),
    );
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onPressed,
      child: Column(
        children: [
          SizedBox(
            height: widget.imageHeight,
            width: widget.imageWidth,
            child: RiveAnimation.asset(
              'assets/animations/rive/gift_box.riv',
              fit: BoxFit.fitWidth,
              controllers: [_riveAnimationController],
            ),
          ),
          TripleBuilder(
            store: _promotionCountdownStore,
            builder: (context, Triple<Duration> triple) => Padding(
              padding: EdgeInsets.only(top: widget.textTopPadding),
              child: Column(
                children: [
                  widget.showLabelEndTime
                      ? Padding(
                          padding: const EdgeInsets.only(bottom: 4.0),
                          child: Text(
                            'OFFER ENDS IN',
                            style: TextStyle(
                                fontFamily: 'Roboto',
                                fontWeight: FontWeight.w400,
                                fontSize: 12,
                                letterSpacing: widget.letterSpacing,
                                color: widget.textColor,
                                decoration: TextDecoration.none),
                          ),
                        )
                      : const SizedBox(),
                  _buildTimer(triple.state),
                ],
              ),
            ),
          )
        ],
      ),
    );
  }
}
