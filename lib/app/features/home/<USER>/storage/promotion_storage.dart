import 'package:play_to_vibe/core/entities/promotion_store_entity.dart';
import 'package:play_to_vibe/core/services/storage/storage_client.dart';
import 'package:play_to_vibe/core/utils/app_storage_keys.dart';

class PromotionStorage {
  final StorageClient _storage;
  PromotionStorage(this._storage);

  Future<bool> setPromotionStoreData(
      [PromotionStoreEntity promotionStore = const PromotionStoreEntity(
          isThereAnOpenModal: false, promotions: [])]) async {
    String promotionStoreJson = promotionStore.toJson();
    return await _storage.write(
        AppStorageKeys.promotionStore, promotionStoreJson);
  }

  Future<PromotionStoreEntity?> getPromotionStoreData() async {
    PromotionStoreEntity? res;
    String? promotionStoreJson =
        await _storage.read(AppStorageKeys.promotionStore);
    if (promotionStoreJson!.isNotEmpty) {
      res = PromotionStoreEntity.fromJson(promotionStoreJson);
    }
    return res;
  }
}
