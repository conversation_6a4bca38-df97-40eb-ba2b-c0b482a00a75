import 'package:play_to_vibe/app/services/remote_config/remote_config_entity.dart';
import 'package:play_to_vibe/core/services/storage/storage_client.dart';
import 'package:play_to_vibe/core/utils/app_storage_keys.dart';

class RemoteConfigStorage {
  final StorageClient _storage;
  RemoteConfigStorage(this._storage);

  Future<bool> setRemoteConfigStoreData(
      RemoteConfigEntity remoteConfigStore) async {
    String remoteConfigStoreJson = remoteConfigStore.toJson();
    return await _storage.write(
        AppStorageKeys.remoteConfigStore, remoteConfigStoreJson);
  }

  Future<RemoteConfigEntity?> getRemoteConfigStoreData() async {
    RemoteConfigEntity? res;
    String? remoteConfigStoreJson =
        await _storage.read(AppStorageKeys.remoteConfigStore);
    if (remoteConfigStoreJson!.isNotEmpty) {
      res = RemoteConfigEntity.fromJson(remoteConfigStoreJson);
    }
    return res;
  }
}
