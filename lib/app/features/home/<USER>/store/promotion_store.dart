import 'package:flutter_triple/flutter_triple.dart';
import 'package:play_to_vibe/app/features/home/<USER>/storage/promotion_storage.dart';
import 'package:play_to_vibe/core/entities/promotion_entity.dart';
import 'package:play_to_vibe/core/entities/promotion_store_entity.dart';

class PromotionStore extends Store<PromotionStoreEntity> {
  final PromotionStorage _promotionStorage;

  PromotionStore(this._promotionStorage) : super(const PromotionStoreEntity()) {
    updatePromotioDataFromStorage();
  }

  void clean([bool complete = false]) {
    update(const PromotionStoreEntity(), force: true);
    if (complete) _promotionStorage.setPromotionStoreData();
  }

  updatePromotioDataFromStorage() async {
    try {
      final promotionFromStorageData =
          await _promotionStorage.getPromotionStoreData() ??
              const PromotionStoreEntity(promotions: []);
      update(state.copyWith(promotions: promotionFromStorageData.promotions),
          force: true);
    } catch (e) {
      print(e);
    }

    setLoading(false);
  }

  setIsOpenedModal(bool value) {
    update(state.copyWith(isThereAnOpenModal: value), force: true);
  }

  addPromotion(PromotionEntity promotion) {
    final existentPromotion = getPromotionById(promotion.identifier);
    if (existentPromotion != null) {
      changePromotionData(promotion);
      return;
    }
    final promotions = [...state.promotions, promotion];
    update(state.copyWith(promotions: promotions), force: true);
    _promotionStorage.setPromotionStoreData(state);
  }

  // removePromotion(String identifier) {
  //   final promotions = state.promotions
  //       .where((element) => element.identifier != identifier)
  //       .toList();
  //   update(state.copyWith(promotions: promotions), force: true);
  //   _promotionStorage.setPromotionStoreData(state);
  // }

  changePromotionData(PromotionEntity promotion) {
    final promotions = state.promotions.map((promotionItem) {
      if (promotionItem.identifier == promotion.identifier) {
        return promotion;
      }
      return promotionItem;
    }).toList();
    update(state.copyWith(promotions: promotions), force: true);
    _promotionStorage.setPromotionStoreData(state);
  }

  PromotionEntity? getPromotionById(String identifier) {
    final res = state.promotions.firstWhere(
      (element) => element.identifier == identifier,
      orElse: () => PromotionEntity(
          identifier: '', name: '', url: '', startTime: DateTime.now()),
    );
    return res.identifier.isEmpty ? null : res;
  }
}
