import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:play_to_vibe/app/widgets/button.dart';
import 'package:play_to_vibe/app/theme/colors.dart';
import 'package:play_to_vibe/core/utils/box_decoration.dart';

class UserRegisterPage extends StatefulWidget {
  final String title;

  const UserRegisterPage({super.key, this.title = 'UserRegisterPage'});

  @override
  UserRegisterPageState createState() => UserRegisterPageState();
}

class UserRegisterPageState extends State<UserRegisterPage> {
  String userName = "";

  @override
  void initState() {
    super.initState();
  }

  Widget getButton() {
    return Button(
      onPressed: handleAccept,
      text: "Play",
    );
  }

  void handleAccept() {
    showDialog(
        context: context,
        builder: (_) => CupertinoAlertDialog(
              title: const Text('Alert'),
              content: const Text("Please read until the end to accept"),
              actions: [
                CupertinoDialogAction(
                  onPressed: () => Navigator.of(context).pop(),
                  isDefaultAction: true,
                  child: const Text("Ok"),
                )
              ],
            ));
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        width: double.infinity,
        height: double.infinity,
        decoration: getDefault(),
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.only(top: 14.0),
            child: Column(
              children: [
                SizedBox(
                    height: 45,
                    child: SvgPicture.asset(
                      'assets/img/svg/logo.svg',
                    )),
                const Padding(
                  padding: EdgeInsets.only(top: 20.0),
                  child: Text(
                    "PROFILE",
                    style: TextStyle(
                        letterSpacing: 1.2,
                        fontFamily: "Roboto",
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                        fontSize: 12),
                  ),
                ),
                const Padding(
                  padding: EdgeInsets.only(top: 15.0),
                  child: Text(
                    "ADD A PHOTO TO YOUR PROFILE",
                    style: TextStyle(
                        letterSpacing: 1.2,
                        fontFamily: "Roboto",
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                        fontSize: 10),
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.only(top: 50.0),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      ClipOval(
                        child: Image.asset(
                          "assets/img/png/avatar.png",
                          width: 105,
                          height: 105,
                          fit: BoxFit.cover,
                        ),
                      ),
                    ],
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.only(
                      top: 114, bottom: 150, left: 30, right: 30),
                  child: Container(
                    constraints: const BoxConstraints(maxWidth: 380),
                    child: Column(
                      children: [
                        Container(
                          height: 50,
                          decoration: const BoxDecoration(
                              color: CustomColors.orangeSoda90,
                              borderRadius: BorderRadius.only(
                                  topLeft: Radius.circular(30),
                                  topRight: Radius.circular(30))),
                          child: TextFormField(
                            onChanged: (value) {
                              userName = value ?? "";
                              print("=$value=>$userName");
                            },
                            onSaved: (value) {
                              userName = value ?? "";
                              print("=$value=>$userName");
                            },
                            validator: (value) {
                              if (value != null && value.length < 3) {
                                return 'a minimum of 3 characters is required';
                              }
                              return null;
                            },
                            decoration: const InputDecoration(
                              contentPadding: EdgeInsets.all(18.0),
                              border: InputBorder.none,
                            ),
                            style: const TextStyle(color: Colors.white),
                          ),
                        ),
                        Container(
                          height: 50,
                          decoration: const BoxDecoration(
                              color: CustomColors.orangeSoda40,
                              borderRadius: BorderRadius.only(
                                  bottomLeft: Radius.circular(30),
                                  bottomRight: Radius.circular(30))),
                          child: Padding(
                            padding:
                                const EdgeInsets.symmetric(horizontal: 18.0),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                const Text(
                                  'YOUR CURRENT RANKING',
                                  style: TextStyle(
                                      letterSpacing: 3,
                                      fontFamily: "Roboto",
                                      fontWeight: FontWeight.bold,
                                      color: Colors.white,
                                      fontSize: 10),
                                ),
                                Row(
                                  children: [
                                    const Padding(
                                      padding:
                                          EdgeInsets.only(top: 6, right: 12.0),
                                      child: Text(
                                        '0',
                                        style: TextStyle(
                                            letterSpacing: 3,
                                            fontFamily: "Roboto",
                                            fontWeight: FontWeight.bold,
                                            color: Colors.white,
                                            fontSize: 10),
                                      ),
                                    ),
                                    Image.asset(
                                      'assets/img/png/fire.png',
                                      width: 20,
                                    ),
                                  ],
                                )
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.symmetric(vertical: 25.0),
                  child: SizedBox(
                    width: double.infinity,
                    child: getButton(),
                  ),
                )
              ],
            ),
          ),
        ),
      ),
    );
  }
//
// JavascriptChannel _toasterJavascriptChannel(BuildContext context) {
//   return JavascriptChannel(
//       name: 'Toaster',
//       onMessageReceived: (JavascriptMessage message) {
//         ScaffoldMessenger.of(context).showSnackBar(
//           SnackBar(content: Text(message.message)),
//         );
//       });
// }
}
