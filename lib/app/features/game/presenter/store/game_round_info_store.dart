import 'package:flutter_triple/flutter_triple.dart';
import 'package:play_to_vibe/app/features/game/domain/entities/game_round_info_entity.dart';
import 'package:play_to_vibe/core/usecase/errors/failure.dart';

class GameRoundInfoStore extends Store<GameRoundInfoEntity> {
  void setCurrentTimerTo() {}

  GameRoundInfoStore()
      : super(const GameRoundInfoEntity(
            currentQuestion: '',
            currentRound: 0,
            totalRounds: 0,
            answering: false));

  setAnsweringState(bool isAnswering) =>
      update(state.copyWith(answering: isAnswering));
  nextRound({String question = ''}) => update(state.copyWith(
      currentQuestion: question,
      currentRound: state.currentRound + 1,
      answering: true));
  restart() {
    update(const GameRoundInfoEntity(
        currentQuestion: 'Question 1',
        currentRound: 1,
        totalRounds: 6,
        answering: true));
  }

  clear() => update(
      const GameRoundInfoEntity(
          currentQuestion: '',
          currentRound: 0,
          totalRounds: 0,
          answering: false),
      force: true);
  setGameRoundInfo(GameRoundInfoEntity data) => update(data);
}
