import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:qr_code_scanner_plus/qr_code_scanner_plus.dart';
import 'package:play_to_vibe/analytics.dart';
import 'package:play_to_vibe/app/features/game/domain/entities/game_entity.dart';
import 'package:play_to_vibe/app/features/game/presenter/controllers/game_controller.dart';
import 'package:play_to_vibe/app/theme/colors.dart';
import 'package:play_to_vibe/app/widgets/app_header.dart';
import 'package:play_to_vibe/app/widgets/gradient_container.dart';
import 'package:play_to_vibe/app/widgets/quycky_modal_progress.dart';
import 'package:play_to_vibe/core/utils/app_routes.dart';
import 'package:play_to_vibe/core/utils/interval_utils.dart';
import 'package:play_to_vibe/core/utils/quycky_icons_icons.dart';
import 'package:play_to_vibe/core/utils/show_dialog_confirmation.dart';
import 'package:play_to_vibe/core/utils/show_message.dart';
import 'package:sizer/sizer.dart';

class ReadGameQrcodePage extends StatefulWidget {
  const ReadGameQrcodePage({super.key});

  @override
  State<ReadGameQrcodePage> createState() => _ReadGameQrcodePageState();
}

class _ReadGameQrcodePageState extends State<ReadGameQrcodePage>
    with SingleTickerProviderStateMixin {
  bool _isVerifyingCode = false;
  final _gameController = Modular.get<GameController>();
  final GlobalKey qrKey = GlobalKey(debugLabel: 'QR');
  final _textEditingCodeFocusNode = FocusNode();
  final _textEditingCodeController = TextEditingController();
  QRViewController? _qrViewController;

  void _onQRViewCreated(QRViewController controller) {
    setState(() {
      _qrViewController = controller;
    });
    _qrViewController!.scannedDataStream.listen((scanData) {
      Analytics.instance.logEvent(name: 'qr_code_readed');
      _qrViewController!.pauseCamera();
      if (scanData.code != null && scanData.code!.isNotEmpty) {
        _handleValidateAndOpenGameByQrCode(scanData.code!);
      }
    });
  }

  @override
  void dispose() {
    _textEditingCodeController.dispose();
    _textEditingCodeFocusNode.dispose();
    super.dispose();
  }

  @override
  void initState() {
    super.initState();
  }

  void _localTest() {
    setTimeout(
        callback: () => _handleLocationCode(
            'https://app.playfultech.io/game/local/20da59f8-81b0-43a8-ac1c-c718423be83d'),
        duration: Duration(seconds: 3));
  }

  void handleGoToLobby([bool isGameStarted = false]) {
    Modular.to.pushReplacementNamed(AppRoutes.gameLobby(), arguments: {
      'isGameStarted': isGameStarted,
    });
  }

  void _pauseCamera() {
    _qrViewController?.pauseCamera();
    setState(() => true);
  }

  void _resumeCamera() {
    _isVerifyingCode = false;
    _qrViewController?.resumeCamera();
    setState(() => true);
  }

  bool isValidCode(String code) {
    if (code.contains('/game/local/')) {
      final parts = code.split('/game/local/');
      if (parts.length >= 2) {
        final locationCode = parts[1].split('/')[0].split('?')[0];
        return locationCode.isNotEmpty;
      }
    }
    return false;
  }

  Widget _buildQrView() {
    final qrCodeView = QRView(
      key: qrKey,
      onQRViewCreated: _onQRViewCreated,
      overlay: QrScannerOverlayShape(
        borderRadius: 10,
        borderLength: 30,
        borderWidth: 10,
        // cutOutSize: (30.h > 254.69 ? 254.69 : 30.h),
      ),
    );

    return SizedBox(
      // constraints: BoxConstraints(maxWidth: 390, maxHeight: 356),
      // margin: EdgeInsets.only(bottom: 3.55.h),
      width: 100.w,
      height: 100.h,
      child: qrCodeView,
      // Container(color: Colors.black),
    );
  }

  void _handleLocationCode(String locationUrl) {
    // Extrai o código de localização da URL
    final parts = locationUrl.split('/game/local/');
    if (parts.length >= 2) {
      final locationCode = parts[1].split('/')[0].split('?')[0];

      ShowMessage(
        message: 'Spot found! preparing to join game...',
        noButton: true,
        duration: Duration(milliseconds: 1300),
      );
      _gameController.setGameMode(EGameMode.spotMatch);

      setTimeout(
          callback: () {
            _gameController.enterGame(
                addBots: false, spotName: '$locationCode-spot-name-');
            handleGoToLobby(true);
          },
          duration: Duration(milliseconds: 1500));

      _resumeCamera();
    } else {
      ShowMessage(
          message: 'Invalid location URL format',
          duration: Duration(seconds: 3));
      _resumeCamera();
    }
  }

  void _handleValidateAndOpenGameByQrCode(String qrText) async {
    if (qrText.isEmpty || _isVerifyingCode) return;

    _isVerifyingCode = true;

    _pauseCamera();

    if (!isValidCode(qrText)) {
      ShowDialogConfirmation(
          onOk: _resumeCamera,
          onClose: _resumeCamera,
          title: 'INVALID QR CODE',
          titleColor: CustomColors.orangeSoda,
          textColor: CustomColors.orangeSoda,
          text:
              "This QR code isn't connected to a Vibetester lobby. Please scan a valid one to join.",
          buttonText: 'CANCEL');
      return;
    }

    _pauseCamera();

    if (qrText.contains('/game/local/')) {
      _handleLocationCode(qrText);
      return;
    }
  }

  Widget _bottomText() {
    return Column(
      children: [
        Text(
          'SCAN A LOCATION',
          style: TextStyle(
              letterSpacing: 1,
              fontFamily: "Roboto",
              fontWeight: FontWeight.w700,
              color: Colors.white,
              fontSize: 16.sp),
        ),
        SizedBox(height: 2.38.h),
        Text(
          'See a Vibetester QR Code nearby?',
          style: TextStyle(
              height: 1.3,
              letterSpacing: 0.1,
              fontWeight: FontWeight.w500,
              fontFamily: "Roboto",
              color: Colors.white,
              fontSize: 16.sp),
        ),
        SizedBox(height: 0.2.h),
        Text(
          'Scan to instantly connect and play with people\naround you — in cafés, bars, or clubs near you.',
          style: TextStyle(
              letterSpacing: 0.25,
              fontFamily: "Roboto",
              fontWeight: FontWeight.w400,
              color: Colors.white,
              fontSize: 16.sp),
        ),
      ],
    );
  }

  Widget _buildBody() {
    return _isVerifyingCode
        ? QuyckyModalProgress(
            loadingIndex: 1,
            state: true,
            child: Container(),
          )
        : SafeArea(
            child: Column(
              children: [
                AppHeader(
                  noTitleSubtitleSpace: true,
                  logoSectionLeftWidget: IconButton(
                      onPressed: handleGoToLobby,
                      icon: Icon(
                        QuyckyIcons.arrow_left_circle,
                        color: Colors.white,
                        size: 23,
                      )),
                ),
                SizedBox(height: 3.31.h),
                Spacer(),
                _bottomText(),
                SizedBox(
                  height: 8.h,
                ),
              ],
            ),
          );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: GradientContainer(
          useDefault: true,
          child: Stack(
            children: [
              _buildQrView(),
              _buildBody(),
            ],
          )),
    );
  }
}
