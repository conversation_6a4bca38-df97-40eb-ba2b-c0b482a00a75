import 'package:flutter/services.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:flutter_triple/flutter_triple.dart';
import 'package:flutter/material.dart';
import 'package:play_to_vibe/app/features/game/domain/entities/game_entity.dart';
import 'package:play_to_vibe/app/features/game/presenter/controllers/game_controller.dart';
import 'package:play_to_vibe/app/features/game/presenter/pages/widgets/game_answer_section.dart';
import 'package:play_to_vibe/app/features/game/presenter/pages/widgets/game_header.dart';
import 'package:play_to_vibe/app/features/game/presenter/pages/widgets/game_players_responses_section.dart';
import 'package:play_to_vibe/app/features/game/presenter/pages/widgets/game_progress_bar.dart';
import 'package:play_to_vibe/app/features/game/presenter/store/game_round_info_store.dart';
import 'package:play_to_vibe/app/features/game/presenter/store/game_store.dart';
import 'package:play_to_vibe/app/widgets/gradient_container.dart';
import 'package:sizer/sizer.dart';

class GamePage extends StatefulWidget {
  final String title;

  const GamePage({super.key, this.title = 'GamePage'});

  @override
  GamePageState createState() => GamePageState();
}

class GamePageState extends State<GamePage> {
  final _gameController = Modular.get<GameController>();
  final _store = Modular.get<GameRoundInfoStore>();
  final _gameStore = Modular.get<GameStore>();
  // late final BackgroundOpacitiesEntity _backgroundOpacities;
  // final ScrollController _scrollController = ScrollController();
  // final Key _scrollGameKey = const ValueKey<String>('_scrollGame');

  // final Completer<WebViewController> _controller =
  //     Completer<WebViewController>();

  @override
  void initState() {
    super.initState();
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.manual, overlays: []);
    // QuestionTemperatureSliderStore questionTemperatureSliderStore =
    //     Modular.get<QuestionTemperatureSliderStore>();

    // _backgroundOpacities = getBackgroundOpacitiesByQuestionTemperature(
    //     questionTemperatureSliderStore.state);
  }

  Widget getProgressBar(int timeInSeconds, String key) {
    return Padding(
        padding: EdgeInsets.only(top: 1.9.h, left: 5.12.w, right: 5.12.w),
        child: GameProgressBar(
          key: Key('_Key_$key'),
          timeInSeconds: timeInSeconds,
          value: 0,
        ));
  }

  Widget animatedSwitch(Widget child) => AnimatedSwitcher(
        duration: const Duration(milliseconds: 400),
        child: child,
      );

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      child: Scaffold(
        body: GradientContainer(
          useDefault: true,
          coldOpacity: 0,
          normalOpacity: 0,
          hotOpacity: 0,
          child: SafeArea(
              child: ScopedBuilder(
            onError: (context, error) => Center(
              child: Text(
                'An error occurred, try again later.',
                style: Theme.of(context)
                    .textTheme
                    .bodySmall
                    ?.copyWith(color: Colors.white),
              ),
            ),
            store: _store,
            onState: (context, state) => Padding(
              padding: const EdgeInsets.only(top: 14.0),
              child: Column(
                children: [
                  const GameHeader(),
                  ScopedBuilder.transition(
                    store: _gameStore,
                    transition: (_, child) {
                      return animatedSwitch(child);
                    },
                    onState: (_, GameEntity state) => animatedSwitch(Column(
                      children: [
                        getProgressBar(state.room.stateTimeInSeconds,
                            state.room.gameState.toString()),
                        state.currentState == EGameState.answering
                            ? Column(
                                children: [
                                  GameAnswerSection(
                                    question: _gameStore.currentQuestion,
                                    setSelectedQuestionOption: _gameController
                                        .setSelectedQuestionOption,
                                    selectedQuestionOption:
                                        state.selectedQuestionOption,
                                  ),
                                ],
                              )
                            : GamePlayersResponsesSection(
                                showPointButtons:
                                    _gameStore.state.showPointButtons,
                                getPlayerImageMemoryData:
                                    _gameStore.getFriendImageDataById,
                                alreadyGavePoints: state.alreadyGavePoint,
                                givePointToPlayer:
                                    _gameController.givePointToPlayer,
                                hidePointButtons:
                                    _gameController.hidePointButtons,
                                getPlayerCurrentAnswerByIdentifier:
                                    _gameController
                                        .getPlayerCurrentAnswerByIdentifier,
                                isReviewingMode:
                                    _gameStore.state.currentState ==
                                        EGameState.reviewing,
                                getPlayerTotalPointsByIdentifier:
                                    _gameController
                                        .getPlayerTotalPointsByIdentifier,
                                question:
                                    _gameStore.currentQuestion.description,
                                players:
                                    _gameController.playersWithoutLocalPlayer,
                                localPlayer: _gameController.localPlayer,
                                receivedPontuations:
                                    _gameStore.state.receivedPontuations,
                                userWhoReceivedPoint:
                                    _gameStore.state.userWhoReceivedPoint,
                                receivedPointType:
                                    _gameStore.state.receivedPontuationType,
                                getPlayerTotalFiresByIdentifier: _gameController
                                    .getPlayerTotalFiresByIdentifier),
                      ],
                    )),
                  )
                ],
              ),
            ),
          )),
        ),
      ),
    );
  }
//
// JavascriptChannel _toasterJavascriptChannel(BuildContext context) {
//   return JavascriptChannel(
//       name: 'Toaster',
//       onMessageReceived: (JavascriptMessage message) {
//         ScaffoldMessenger.of(context).showSnackBar(
//           SnackBar(content: Text(message.message)),
//         );
//       });
// }
}
