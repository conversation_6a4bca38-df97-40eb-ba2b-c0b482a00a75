import 'dart:typed_data';

import 'package:flutter/material.dart';
import 'package:play_to_vibe/app/features/game/domain/entities/socket_user_entity.dart';
import 'package:play_to_vibe/app/features/game/presenter/pages/widgets/game_player_review_item.dart';
import 'package:play_to_vibe/app/features/user/domain/entities/user_entity.dart';

TextStyle buttonDefaultTextStyle(Color color, {double fontSize = 15}) =>
    TextStyle(
        letterSpacing: 3,
        fontFamily: "Roboto",
        fontWeight: FontWeight.bold,
        color: color,
        fontSize: fontSize);

class GamePlayerReviewSection extends StatefulWidget {
  final List<SocketUserEntity> players;
  final List<SocketUserEntity> rankedPlayers;
  final UserEntity localPlayer;
  final int Function(String identifier) getPlayerTotalPointsByIdentifier;
  final Uint8List? Function(String id) getPlayerImageMemoryData;

  const GamePlayerReviewSection(
      {super.key,
      required this.players,
      required this.rankedPlayers,
      required this.localPlayer,
      required this.getPlayerImageMemoryData,
      required this.getPlayerTotalPointsByIdentifier});

  @override
  State<GamePlayerReviewSection> createState() => _GamePlayerReviewSection();
}

class _GamePlayerReviewSection extends State<GamePlayerReviewSection> {
  final _defaultPlayer = [
    const UserEntity(name: 'ROB'),
    const UserEntity(name: 'NICK_32'),
    const UserEntity(name: 'AISHA')
  ];
  UserEntity getPlayer(int index) {
    //.players
    if (!widget.rankedPlayers.asMap().containsKey(index)) {
      return _defaultPlayer[index];
    }
    return widget.rankedPlayers[index].user;
  }

  Widget getPlayerResponseItem(int index,
      {totalPoints = 0, showAddButton = true}) {
    UserEntity player = getPlayer(index);
    // index == -1
    //     ? widget.localPlayer // ?? const UserEntity(name: 'ME')
    //     : getPlayer(index);

    return getPlayerItem(player,
        showAddButton: showAddButton, totalPoints: totalPoints);
  }

  onAvatarPressed() {}

  Widget getPlayerItem(UserEntity player,
      {showAddButton = true, totalPoints = 0}) {
    // String identifier = player.identifier ?? '';
    return GamePlayerReviewItem(
      player: player,
      isLocalPlayer: true,
      avatarSize: 40,
      playerImageMemoryData: widget.getPlayerImageMemoryData(player.id),
      points: totalPoints,
      showAddButton: showAddButton,
      onAvatarPressed: onAvatarPressed,
    );
  }

  List<Widget> getPlayersResponseItems() {
    List<Widget> response = [];
    try {
      for (int i = 0; i < widget.rankedPlayers.length; i++) {
        if (i > 0) {
          response.add(const SizedBox(
            height: 9,
          ));
        }
        final identifier = widget.rankedPlayers[i].user.identifier ?? '';
        response.add(getPlayerResponseItem(i,
            totalPoints: widget.getPlayerTotalPointsByIdentifier(identifier),
            showAddButton: identifier != widget.localPlayer.identifier));
      }
      // response.add(const Spacer());
    } catch (err) {
      print('err:==>$err');
    }
    return response;
  }

  Widget getWidget() =>
      Column(mainAxisSize: MainAxisSize.max, children: getPlayersResponseItems()
          // [
          //       ...getPlayersResponseItems(),
          //       getPlayerResponseItem(-1, showAddButton: false),
          //        const Spacer()
          //     ]
          );

  @override
  Widget build(BuildContext context) {
    return getWidget();
  }
}
