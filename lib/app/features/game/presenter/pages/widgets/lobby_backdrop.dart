import 'package:flutter/material.dart';
import 'package:play_to_vibe/app/features/game/presenter/pages/widgets/player_avatar_animated.dart';
import 'package:play_to_vibe/app/features/user/domain/entities/user_entity.dart';
import 'package:play_to_vibe/core/utils/interval_utils.dart';

class LobbyBackdrop extends StatefulWidget {
  final Widget child;
  final Duration? duration;
  final Offset? offset;
  final UserEntity friendData;
  const LobbyBackdrop(
      {super.key,
      required this.friendData,
      required this.child,
      this.offset,
      this.duration});

  @override
  _LobbyBackdrop createState() => _LobbyBackdrop();
}

class _LobbyBackdrop extends State<LobbyBackdrop> {
  void close() => Navigator.pop(context);

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    if (widget.duration != null) {
      setTimeout(duration: widget.duration, callback: () => close());
    }
  }

  Widget getItem() {
    return widget.offset != null
        ? Positioned(
            left: widget.offset!.dx,
            top: widget.offset!.dy,
            child: PlayerAvatarAnimated(
              alignment: Alignment.center,
              imagePath: widget.friendData.avatarUrl,
              name: widget.friendData.name,
              visible: true,
            ))
        : Container();
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        widget.child,
        Container(
            height: double.infinity,
            width: double.infinity,
            color: const Color(0x78000000),
            child: Stack(
              children: [
                getItem(),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Padding(
                      padding: const EdgeInsets.only(top: 275.0),
                      child: Text(
                        "${widget.friendData.name}\nHAS JOINED!",
                        style: const TextStyle(
                            fontFamily: "Roboto",
                            fontWeight: FontWeight.w600,
                            fontSize: 15,
                            letterSpacing: 3,
                            color: Colors.white,
                            height: 1.3,
                            decoration: TextDecoration.none),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ],
                ),
              ],
            )),
      ],
    );
  }
}
