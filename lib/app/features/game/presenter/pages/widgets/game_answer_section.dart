import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:gap/gap.dart';
import 'package:play_to_vibe/app/features/game/domain/entities/question_entity.dart';
import 'package:play_to_vibe/app/features/game/domain/entities/question_option_entity.dart';
import 'package:play_to_vibe/app/features/game/presenter/controllers/game_controller.dart';
import 'package:play_to_vibe/app/features/game/presenter/pages/widgets/game_question_option.dart';
import 'package:play_to_vibe/app/theme/colors.dart';
import 'package:play_to_vibe/app/widgets/button.dart';
import 'package:sizer/sizer.dart';

TextStyle buttonDefaultTextStyle(Color color, {double fontSize = 15}) =>
    TextStyle(
        letterSpacing: 3,
        fontFamily: "Roboto",
        fontWeight: FontWeight.bold,
        color: color,
        fontSize: fontSize);

class GameAnswerSection extends StatefulWidget {
  final String title;
  final QuestionOptionEntity? selectedQuestionOption;
  final QuestionEntity question;
  final void Function(QuestionOptionEntity? v) setSelectedQuestionOption;

  const GameAnswerSection(
      {super.key,
      this.title = '',
      required this.question,
      required this.selectedQuestionOption,
      required this.setSelectedQuestionOption});

  @override
  State<GameAnswerSection> createState() => _GameAnswerSection();
}

class _GameAnswerSection extends State<GameAnswerSection> {
  final controller = Modular.get<GameController>();
  bool isLessOrEqualsToMinimumLayoutHeight = false;

  Widget getButton() => Button(
        text: "SEND",
        onPressed: controller.sendAnswer,
        autoSized: true,
      );

  List<Widget> getOptions() {
    if (widget.question.type == 'choice') {
      final result = <Widget>[];
      final optionsLabels = ['A', 'B', 'C', 'D', 'E'];
      for (int i = 0; i < widget.question.questionsOptions.length; i++) {
        final questionOptionData = widget.question.questionsOptions[i];
        result.add(GameQuestionOption(
            label: optionsLabels[i],
            optionData: questionOptionData,
            onTap: () => widget.setSelectedQuestionOption(questionOptionData),
            isSelected:
                questionOptionData.id == widget.selectedQuestionOption?.id));
        result.add(SizedBox(
          height: 1.77.h,
        ));
      }
      return result;
    }

    return [
      Padding(
        padding: EdgeInsets.only(bottom: 1.77.h),
        child: TextFormField(
          controller: controller.userResponseTextEditController,
          onChanged: handleTextChange,
          textInputAction: TextInputAction.send,
          onFieldSubmitted: (val) => controller.sendAnswer(),
          decoration: const InputDecoration(
            hintText: 'Type here...',
            hintStyle: TextStyle(
                fontFamily: "SFProText", color: Colors.grey, fontSize: 23),
            filled: true,
            fillColor: Colors.white,
            border: OutlineInputBorder(
                borderSide: BorderSide(
                  width: 0,
                  style: BorderStyle.none,
                ),
                borderRadius: BorderRadius.all(Radius.circular(30))),
            contentPadding: EdgeInsets.all(32),
          ),
          style: const TextStyle(
              fontFamily: "SFProText",
              color: CustomColors.americanPurple,
              fontSize: 18),
        ),
      ),
    ];
  }

  void handleTextChange(String newText) {
    if (widget.selectedQuestionOption != null) {
      widget.setSelectedQuestionOption(null);
    }
  }

  Widget getWidget() {
    return Center(
      child: Container(
          constraints: BoxConstraints(maxWidth: 90.w),
          child: Column(children: [
            Container(
              constraints: BoxConstraints(maxWidth: 89.w),
              height: 11.h,
              margin: EdgeInsets.symmetric(vertical: 1.77.h),
              child: Text(
                widget.question.description,
                textAlign: TextAlign.center,
                overflow: TextOverflow.ellipsis,
                maxLines: 3,
                style: TextStyle(
                    fontFamily: "Roboto",
                    height: 1.1,
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    fontSize: 21),
              ),
            ),
            ...getOptions(),
            SizedBox(
              width: 90.w,
              height: 6.04.h,
              child: getButton(),
            ),
          ])),
    );
  }

  @override
  Widget build(BuildContext context) {
    return getWidget();
  }
}
