import 'package:flutter/material.dart';
import 'dart:math' as math;

import 'package:play_to_vibe/app/widgets/avatar.dart';

class PlayerAvatarAnimated extends StatefulWidget {
  final String name;
  final bool visible;
  final AlignmentGeometry alignment;
  final String? imagePath;
  final void Function()? onPressed;
  const PlayerAvatarAnimated(
      {super.key,
      this.name = '',
      this.imagePath,
      this.onPressed,
      this.visible = true,
      this.alignment = Alignment.center});
  @override
  _PlayerAvatarAnimatedState createState() => _PlayerAvatarAnimatedState();
}

class _PlayerAvatarAnimatedState extends State<PlayerAvatarAnimated>
    with TickerProviderStateMixin {
  late final AnimationController _controller =
      AnimationController(vsync: this, duration: const Duration(seconds: 15))
        ..repeat();

  late AnimationController controller;

  @override
  void initState() {
    super.initState();
    controller = AnimationController(vsync: this);

    controller.repeat(min: 0.0, max: 1.0, period: const Duration(seconds: 15));
  }

  onPressed() {
    if (widget.onPressed != null) {
      widget.onPressed!();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      key: widget.key,
      alignment: Alignment.center,
      children: <Widget>[
        RotationTransition(
          turns: controller,
          child: Align(
            alignment: widget.alignment,
            child: AnimatedBuilder(
              animation: _controller,
              builder: (_, child) {
                return Transform.rotate(
                  angle: -_controller.value * 2 * math.pi,
                  child: child,
                );
              },
              child: Visibility(
                visible: widget.visible,
                child: SizedBox(
                  height: 84,
                  child: Column(
                    children: [
                      Avatar(
                          imagePath: widget.imagePath,
                          addPhotoButton: false,
                          onPressed: onPressed,
                          size: 60),
                      Padding(
                        padding: const EdgeInsets.only(top: 4.0),
                        child: Text(
                          widget.name,
                          style: const TextStyle(
                              letterSpacing: 1.2,
                              fontFamily: "Roboto",
                              fontWeight: FontWeight.normal,
                              color: Colors.white,
                              fontSize: 12,
                              decoration: TextDecoration.none),
                        ),
                      )
                    ],
                  ),
                ),
              ),
            ),
          ),
        )
      ],
    );
  }
}
