// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:dartz/dartz.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:play_to_vibe/app/features/friendship/domain/entities/game_invite_entity.dart';
import 'package:play_to_vibe/app/features/game/domain/repositories/game_repository.dart';
import 'package:play_to_vibe/app/features/user/domain/entities/user_entity.dart';
import 'package:play_to_vibe/app/features/user/domain/usecases/get_user_by_id_usecase.dart';
import 'package:play_to_vibe/core/usecase/errors/failure.dart';
import 'package:play_to_vibe/core/usecase/usecase.dart';

class GetGameInvitationsByUserIdUseCase
    implements UseCase<List<GameInviteEntity>, int> {
  final GetUserByIdUseCase getUserByIdUseCase;

  GetGameInvitationsByUserIdUseCase(
    this.getUserByIdUseCase,
  );

  @override
  Future<Either<Failure, List<GameInviteEntity>>> call(int userId) async {
    try {
      // Get game invitations from API
      final gameRepository = Modular.get<IGameRepository>();
      final gameInvitationsResult =
          await gameRepository.getGameInvitationsByUserId();

      return gameInvitationsResult.fold(
        (failure) => Left(failure),
        (gameInvitationModels) async {
          // For each invitation, get the player data using user_id
          List<GameInviteEntity> enrichedInvitations = [];

          for (var invitationModel in gameInvitationModels) {
            UserEntity player = const UserEntity(name: 'Unknown Player');

            // Get the user_id from the invitation (the one who sent the invite)
            // According to the API structure, user_id is the sender
            if (invitationModel.userId > 0) {
              final userResult =
                  await getUserByIdUseCase(invitationModel.userId);
              userResult.fold(
                (failure) {
                  // Keep default player if user fetch fails
                },
                (user) {
                  player = user;
                },
              );
            }

            // Convert model to entity with enriched player data
            final enrichedInvitation = GameInviteEntity(
              id: invitationModel.id,
              gameId: '',
              roomName: invitationModel.roomName,
              situation: 'active',
              createdAt: invitationModel.createdAt,
              expiresAt: invitationModel.createdAt.add(Duration(seconds: 45)),
              player: player,
            );
            enrichedInvitations.add(enrichedInvitation);
          }

          return Right(enrichedInvitations);
        },
      );
    } catch (e) {
      return const Left(ServerFailure());
    }
  }
}
