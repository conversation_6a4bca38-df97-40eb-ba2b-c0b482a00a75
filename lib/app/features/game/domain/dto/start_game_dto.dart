// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'dart:convert';

import 'package:play_to_vibe/core/entities/abs_mappable.dart';

class StartGameDTO implements AbsMappable {
  final String roomName;
  StartGameDTO({
    required this.roomName,
  });

  StartGameDTO copyWith({
    String? roomName,
  }) {
    return StartGameDTO(
      roomName: roomName ?? this.roomName,
    );
  }

  @override
  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'roomName': roomName,
    };
  }

  factory StartGameDTO.fromMap(Map<String, dynamic> map) {
    return StartGameDTO(
      roomName: map['roomName'] as String,
    );
  }

  @override
  String toJson() => json.encode(toMap());

  factory StartGameDTO.fromJson(String source) =>
      StartGameDTO.fromMap(json.decode(source) as Map<String, dynamic>);

  @override
  String toString() => 'StartGameDTO(roomName: $roomName)';

  @override
  bool operator ==(covariant StartGameDTO other) {
    if (identical(this, other)) return true;

    return other.roomName == roomName;
  }

  @override
  int get hashCode => roomName.hashCode;
}
