// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'dart:convert';

import 'package:play_to_vibe/app/features/game/domain/entities/game_entity.dart';
import 'package:play_to_vibe/core/entities/abs_mappable.dart';

class ChangeGameStateDTO implements AbsMappable {
  final String roomName;
  final int round;
  final EGameState state;

  ChangeGameStateDTO({
    required this.roomName,
    required this.round,
    required this.state,
  });

  ChangeGameStateDTO copyWith({
    String? roomName,
    int? round,
    EGameState? state,
  }) {
    return ChangeGameStateDTO(
      roomName: roomName ?? this.roomName,
      round: round ?? this.round,
      state: state ?? this.state,
    );
  }

  @override
  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'roomName': roomName,
      'round': round,
      'state': state.toString(),
    };
  }

  factory ChangeGameStateDTO.fromMap(Map<String, dynamic> map) {
    return ChangeGameStateDTO(
      roomName: map['roomName'] as String,
      round: map['round'] as int,
      state: EGameState.fromString(map['state'] as String),
    );
  }

  @override
  String toJson() => json.encode(toMap());

  factory ChangeGameStateDTO.fromJson(String source) =>
      ChangeGameStateDTO.fromMap(json.decode(source) as Map<String, dynamic>);

  @override
  String toString() =>
      'ChangeGameStateDTO(roomName: $roomName, round: $round, state: $state)';

  @override
  bool operator ==(covariant ChangeGameStateDTO other) {
    if (identical(this, other)) return true;

    return other.roomName == roomName &&
        other.round == round &&
        other.state == state;
  }

  @override
  int get hashCode => roomName.hashCode ^ round.hashCode ^ state.hashCode;
}
