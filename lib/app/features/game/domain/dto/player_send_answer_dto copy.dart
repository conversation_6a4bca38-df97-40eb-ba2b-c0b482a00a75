// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'dart:convert';

import 'package:play_to_vibe/core/entities/abs_mappable.dart';
import 'package:play_to_vibe/core/utils/type_converters.dart';

class PlayerSendAnswerDTO implements AbsMappable {
  final String roomName;
  final int round;
  final String answer;
  final int? questionOptionId;
  final bool? isCorrectAnswer;

  PlayerSendAnswerDTO({
    required this.roomName,
    required this.round,
    required this.answer,
    this.questionOptionId,
    this.isCorrectAnswer,
  });

  PlayerSendAnswerDTO copyWith({
    String? roomName,
    int? round,
    String? answer,
    int? questionOptionId,
    bool? isCorrectAnswer,
  }) {
    return PlayerSendAnswerDTO(
      roomName: roomName ?? this.roomName,
      round: round ?? this.round,
      answer: answer ?? this.answer,
      questionOptionId: questionOptionId ?? this.questionOptionId,
      isCorrectAnswer: isCorrectAnswer ?? this.isCorrectAnswer,
    );
  }

  @override
  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'roomName': roomName,
      'round': round,
      'answer': answer,
      'question_option_id': questionOptionId,
      'is_correct_answer': isCorrectAnswer,
    };
  }

  factory PlayerSendAnswerDTO.fromMap(Map<String, dynamic> map) {
    return PlayerSendAnswerDTO(
      roomName: map['roomName'] as String,
      round: dynamicToInt(map['round']),
      answer: map['answer'] as String,
      questionOptionId: map['question_option_id'] != null
          ? dynamicToInt(map['question_option_id'])
          : null,
      isCorrectAnswer: map['is_correct_answer'] != null
          ? map['is_correct_answer'] as bool
          : null,
    );
  }

  @override
  String toJson() => json.encode(toMap());

  factory PlayerSendAnswerDTO.fromJson(String source) =>
      PlayerSendAnswerDTO.fromMap(json.decode(source) as Map<String, dynamic>);

  @override
  String toString() {
    return 'PlayerSendAnswerDTO(roomName: $roomName, round: $round, answer: $answer, questionOptionId: $questionOptionId, isCorrectAnswer: $isCorrectAnswer)';
  }

  @override
  bool operator ==(covariant PlayerSendAnswerDTO other) {
    if (identical(this, other)) return true;

    return other.roomName == roomName &&
        other.round == round &&
        other.answer == answer &&
        other.questionOptionId == questionOptionId &&
        other.isCorrectAnswer == isCorrectAnswer;
  }

  @override
  int get hashCode {
    return roomName.hashCode ^
        round.hashCode ^
        answer.hashCode ^
        questionOptionId.hashCode ^
        isCorrectAnswer.hashCode;
  }
}
