// To parse this JSON data, do
//
//     final pontuationEntity = pontuationEntityFromJson(jsonString);

import 'package:equatable/equatable.dart';
import 'dart:convert';

import 'package:play_to_vibe/core/utils/type_converters.dart';

class PontuationEntity extends Equatable {
  final int currentRound;
  final String userThatSetPontuationIdentifier;
  final String userThatReceivePontuationIdentifier;
  final int pontuation;
  final int questionId;
  const PontuationEntity(
      {required this.currentRound,
      required this.userThatSetPontuationIdentifier,
      required this.userThatReceivePontuationIdentifier,
      required this.pontuation,
      this.questionId = 0});

  PontuationEntity copyWith({
    int? currentRound,
    String? userThatSetPontuationIdentifier,
    String? userThatReceivePontuationIdentifier,
    int? pontuation,
    int? questionId,
  }) =>
      PontuationEntity(
        currentRound: currentRound ?? this.currentRound,
        userThatSetPontuationIdentifier: userThatSetPontuationIdentifier ??
            this.userThatSetPontuationIdentifier,
        userThatReceivePontuationIdentifier:
            userThatReceivePontuationIdentifier ??
                this.userThatReceivePontuationIdentifier,
        pontuation: pontuation ?? this.pontuation,
        questionId: questionId ?? this.questionId,
      );

  factory PontuationEntity.fromRawJson(String str) =>
      PontuationEntity.fromMap(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory PontuationEntity.fromMap(Map<String, dynamic> json) {
    return PontuationEntity(
      currentRound: dynamicToInt(json["currentRound"]),
      userThatSetPontuationIdentifier:
          dynamicToString(json["userThatSetPontuationIdentifier"]),
      userThatReceivePontuationIdentifier:
          dynamicToString(json["userThatReceivePontuationIdentifier"]),
      pontuation: dynamicToInt(json["pontuation"]),
      questionId: dynamicToInt(json["questionId"]),
    );
  }

  Map<String, dynamic> toJson() => {
        "currentRound": currentRound,
        "userThatSetPontuationIdentifier": userThatSetPontuationIdentifier,
        "userThatReceivePontuationIdentifier":
            userThatReceivePontuationIdentifier,
        "pontuation": pontuation,
        "questionId": questionId,
      };

  @override
  List<Object?> get props => [
        currentRound,
        userThatSetPontuationIdentifier,
        userThatReceivePontuationIdentifier,
        pontuation,
      ];
}
