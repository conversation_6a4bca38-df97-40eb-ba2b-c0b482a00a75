// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'dart:convert';

import 'package:equatable/equatable.dart';
import 'package:play_to_vibe/app/features/user/domain/entities/user_entity.dart';

class SocketUserEntity extends Equatable {
  final String? connectionId;
  final UserEntity user;
  final bool roomOwner;
  final String? userName;
  final String? roomName;
  final DateTime? updatedAt;
  const SocketUserEntity(
      {this.connectionId,
      required this.user,
      this.userName,
      this.roomName,
      this.updatedAt,
      this.roomOwner = false});

  @override
  // TODO: implement props
  List<Object> get props {
    return [];
  }

  SocketUserEntity copyWith({
    String? connectionId,
    UserEntity? user,
    String? userName,
    String? roomName,
    DateTime? updatedAt,
    bool? roomOwner,
  }) {
    return SocketUserEntity(
        connectionId: connectionId ?? this.connectionId,
        user: user ?? this.user,
        userName: userName ?? this.userName,
        roomName: roomName ?? this.roomName,
        updatedAt: updatedAt ?? this.updatedAt,
        roomOwner: roomOwner ?? this.roomOwner);
  }

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'connectionId': connectionId,
      'user': user.toMap(),
      'userName': userName,
      'roomName': roomName,
      'roomOwner': roomOwner,
      'updated_at': updatedAt?.millisecondsSinceEpoch,
    };
  }

  factory SocketUserEntity.fromMap(Map<String, dynamic> map) {
    return SocketUserEntity(
      connectionId: map['connectionId'] as String,
      user: UserEntity.fromJson(map['user'] as Map<String, dynamic>),
      userName: map['userName'] ?? '',
      roomOwner: map['roomOwner'] as bool,
      roomName: map['roomName'] as String,
      updatedAt: DateTime.tryParse(map['updated_at']),
    );
  }

  String toJson() => json.encode(toMap());

  factory SocketUserEntity.fromJson(String source) =>
      SocketUserEntity.fromMap(json.decode(source) as Map<String, dynamic>);

  @override
  bool get stringify => true;
}
