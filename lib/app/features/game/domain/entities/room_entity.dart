// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'dart:convert';

import 'package:equatable/equatable.dart';

import 'package:play_to_vibe/app/features/game/domain/entities/answer_entity.dart';
import 'package:play_to_vibe/app/features/game/domain/entities/game_entity.dart';
import 'package:play_to_vibe/app/features/game/domain/entities/pontuation_entity.dart';
import 'package:play_to_vibe/app/features/game/domain/entities/question_entity.dart';
import 'package:play_to_vibe/app/features/game/domain/entities/socket_user_entity.dart';

class RoomEntity extends Equatable {
  final String? roomName;
  final String? spotName;
  final List<SocketUserEntity> users;
  final int flagPlayersReady;
  final int flagMaxPlayers;
  final int flagCurrentRound;
  final int flagPlayersFinished;
  final List<QuestionEntity> questions;
  final List<AnswerEntity> answers;
  final EGameMode gameMode;
  final EGameState gameState;
  final List<PontuationEntity> pontuations;
  final int stateTimeInSeconds;
  final int timeWaitingStartingGame;
  final int timeAnsweringRound;
  final int timeReviewingRound;
  final int numRounds;
  final int timeAnsweringQuestionDirect;
  final int timeAnsweringQuestionMultiple;

  const RoomEntity({
    this.roomName = '',
    this.spotName,
    required this.users,
    required this.flagPlayersReady,
    required this.flagCurrentRound,
    required this.flagPlayersFinished,
    required this.flagMaxPlayers,
    required this.questions,
    required this.answers,
    required this.gameMode,
    required this.pontuations,
    required this.gameState,
    this.timeWaitingStartingGame = 0,
    this.numRounds = 6,
    this.timeAnsweringRound = 0,
    this.timeReviewingRound = 0,
    this.stateTimeInSeconds = 0,
    this.timeAnsweringQuestionDirect = 0,
    this.timeAnsweringQuestionMultiple = 0,
  });

  @override
  // TODO: implement props
  List<Object> get props {
    return [];
  }

  RoomEntity copyWith({
    String? roomName,
    String? spotName,
    List<SocketUserEntity>? users,
    int? flagPlayersReady,
    int? flagCurrentRound,
    int? flagPlayersFinished,
    int? flagMaxPlayers,
    EGameMode? gameMode,
    List<QuestionEntity>? questions,
    List<AnswerEntity>? answers,
    List<PontuationEntity>? pontuations,
    int? stateTimeInSeconds,
    EGameState? gameState,
    int? timeWaitingStartingGame,
    int? timeAnsweringRound,
    int? timeReviewingRound,
    int? timeAnsweringQuestionDirect,
    int? timeAnsweringQuestionMultiple,
    int? numRounds,
  }) {
    return RoomEntity(
      flagMaxPlayers: flagMaxPlayers ?? this.flagMaxPlayers,
      roomName: roomName ?? this.roomName,
      users: users ?? this.users,
      flagPlayersReady: flagPlayersReady ?? this.flagPlayersReady,
      flagCurrentRound: flagCurrentRound ?? this.flagCurrentRound,
      flagPlayersFinished: flagPlayersFinished ?? this.flagPlayersFinished,
      gameMode: gameMode ?? this.gameMode,
      questions: questions ?? this.questions,
      answers: answers ?? this.answers,
      pontuations: pontuations ?? this.pontuations,
      gameState: gameState ?? this.gameState,
      numRounds: numRounds ?? this.numRounds,
      stateTimeInSeconds: stateTimeInSeconds ?? this.stateTimeInSeconds,
      timeWaitingStartingGame:
          timeWaitingStartingGame ?? this.timeWaitingStartingGame,
      timeAnsweringRound: timeAnsweringRound ?? this.timeAnsweringRound,
      timeReviewingRound: timeReviewingRound ?? this.timeReviewingRound,
      timeAnsweringQuestionDirect:
          timeAnsweringQuestionDirect ?? this.timeAnsweringQuestionDirect,
      timeAnsweringQuestionMultiple:
          timeAnsweringQuestionMultiple ?? this.timeAnsweringQuestionMultiple,
      spotName: spotName ?? this.spotName,
    );
  }

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'roomName': roomName,
      'users': users.map((x) => x.toMap()).toList(),
      'flagPlayersReady': flagPlayersReady,
      'flagCurrentRound': flagCurrentRound,
      'flagPlayersFinished': flagPlayersFinished,
      'flagMaxPlayers': flagMaxPlayers,
      'questions': questions.map((e) => e.toMap()).toList(),
      'answers': answers.map((x) => x.toMap()).toList(),
      'pontuations': pontuations.map((x) => x.toJson()).toList(),
      'gameMode': gameMode.toString(),
      'stateTimeInSeconds': stateTimeInSeconds,
      'timeWaitingStartingGame': timeWaitingStartingGame,
      'timeAnsweringRound': timeAnsweringRound,
      'numRounds': numRounds,
      'timeReviewingRound': timeReviewingRound,
      'timeAnsweringQuestionDirect': timeAnsweringQuestionDirect,
      'spotName': spotName,
      'timeAnsweringQuestionMultiple': timeAnsweringQuestionMultiple,
    };
  }

  factory RoomEntity.fromMap(Map<String, dynamic> map) => RoomEntity(
        roomName: map['roomName'] ?? '',
        users: List<SocketUserEntity>.from(
          (map['users'] as List<dynamic>).map<SocketUserEntity>(
            (x) => SocketUserEntity.fromMap(x as Map<String, dynamic>),
          ),
        ),
        flagPlayersReady: (map['flagPlayersReady'] ?? 0) as int,
        stateTimeInSeconds: (map['stateTimeInSeconds'] ?? 0) as int,
        flagMaxPlayers: (map['flagMaxPlayers'] ?? 0) as int,
        flagCurrentRound: (map['flagCurrentRound'] ?? 0) as int,
        flagPlayersFinished: (map['flagPlayersFinished'] ?? 0) as int,
        questions: List<QuestionEntity>.from(
          (map['questions'] as List<dynamic>).map<QuestionEntity>(
            (x) => QuestionEntity.fromMap(x as Map<String, dynamic>),
          ),
        ),
        answers: List<AnswerEntity>.from(
          (map['answers'] as List<dynamic>).map<AnswerEntity>(
            (x) => AnswerEntity.fromMap(x as Map<String, dynamic>),
          ),
        ),
        pontuations: List<PontuationEntity>.from(
          (map['pontuations'] as List<dynamic>).map<PontuationEntity>(
            (x) => PontuationEntity.fromMap(x as Map<String, dynamic>),
          ),
        ),
        gameMode: EGameMode.values.byName(map['gameMode']),
        gameState: EGameState.values.byName(map['gameState']),
        timeWaitingStartingGame: (map['timeWaitingStartingGame'] ?? 0) as int,
        timeAnsweringRound: (map['timeAnsweringRound'] ?? 0) as int,
        timeReviewingRound: (map['timeReviewingRound'] ?? 0) as int,
        numRounds: (map['numRounds'] ?? 6) as int,
        timeAnsweringQuestionDirect:
            (map['timeAnsweringQuestionDirect'] ?? 0) as int,
        timeAnsweringQuestionMultiple:
            (map['timeAnsweringQuestionMultiple'] ?? 0) as int,
        spotName: map['spotName'],
      );

  String toJson() => json.encode(toMap());

  factory RoomEntity.fromJson(String source) =>
      RoomEntity.fromMap(json.decode(source) as Map<String, dynamic>);

  @override
  bool get stringify => true;
}
