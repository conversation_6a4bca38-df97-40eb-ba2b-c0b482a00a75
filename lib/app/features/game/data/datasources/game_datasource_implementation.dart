import 'package:play_to_vibe/app/features/game/data/datasources/endpoints/game_endpoints.dart';
import 'package:play_to_vibe/app/features/game/data/datasources/game_datasource.dart';
import 'package:play_to_vibe/app/features/game/data/models/friend_invite_response_model.dart';
import 'package:play_to_vibe/app/features/game/data/models/game_invitation_model.dart';
import 'package:play_to_vibe/app/features/game/domain/entities/friend_invite_params_entity.dart';
import 'package:play_to_vibe/core/http_client/http_client.dart';
import 'package:play_to_vibe/core/usecase/errors/exceptions.dart';

class GameDatasourceImplementation implements IGameDatasource {
  final HttpClient client;

  GameDatasourceImplementation(this.client);

  @override
  Future<FriendInviteResponseModel> inviteFriendToPlay(
      FriendInviteParamsEntity params) async {
    final response = await client.get(GameEndpoints.inviteFriendToPlay(params));
    if (response.statusCode == 200) {
      // final body = {
      //   "friend_id": dynamicToInt(params.id),
      //   "room_id": "",
      //   "room_name": params.roomName
      // };
      // final newGameInviteResponse =
      //     await client.post(GameEndpoints.createGameInvite(), body: body);
      return FriendInviteResponseModel.fromJson(response.data);
    }
    throw ServerException();
  }

  @override
  Future<List<GameInvitationResponseModel>> getGameInvitationsByUserId() async {
    final response =
        await client.get(GameEndpoints.getGameInvitationsByUserId());
    if (response.statusCode == 200 && response.data['data'] != null) {
      final data = response.data['data'];
      final res = (data as List<dynamic>)
          .map((item) => GameInvitationResponseModel.fromJson(
              item as Map<String, dynamic>))
          .toList();

      return res;
    }
    throw ServerException();
  }
}
