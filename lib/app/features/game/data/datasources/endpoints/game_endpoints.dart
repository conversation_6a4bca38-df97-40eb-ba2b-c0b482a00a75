import 'package:play_to_vibe/app/features/game/domain/entities/friend_invite_params_entity.dart';
import 'package:play_to_vibe/core/utils/server_url.dart';

class GameEndpoints {
  static String currentGame() => ServerUrl.getConfiguredUrl("game/newestTerm");
  static String inviteFriendToPlay(FriendInviteParamsEntity params) =>
      ServerUrl.getConfiguredUrl(
          "users/invite/game/${params.roomName}/${params.friendIdentifier}");
  static String getGameInvitationsByUserId() =>
      ServerUrl.getConfiguredUrl("gameInvitations/byUserId");
  static String createGameInvite() =>
      ServerUrl.getConfiguredUrl("gameInvitations");
}
