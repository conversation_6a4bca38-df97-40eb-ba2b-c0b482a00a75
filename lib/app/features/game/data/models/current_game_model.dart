import 'dart:convert';

import 'package:play_to_vibe/app/features/game/domain/entities/current_game_entity.dart';

class CurrentGameModel extends CurrentGameEntity {
  const CurrentGameModel({required currentRound, required totalRounds})
      : super(totalRounds: totalRounds, currentRound: currentRound);

  factory CurrentGameModel.fromRawJson(String str) =>
      CurrentGameModel.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory CurrentGameModel.fromJson(Map<String, dynamic> json) =>
      CurrentGameModel(
        totalRounds: json["totalRounds"],
        currentRound: json["currentRound"],
      );

  Map<String, dynamic> toJson() => {
        "totalRounds": totalRounds,
        "currentRound": currentRound,
      };
}
