import 'dart:convert';

import 'package:play_to_vibe/app/features/game/domain/entities/friend_invite_response_entity.dart';
import 'package:play_to_vibe/app/features/user/domain/entities/user_entity.dart';

class FriendInviteResponseModel extends FriendInviteResponseEntity {
  FriendInviteResponseModel({
    required user,
    required roomName,
  }) : super(user: user, roomName: roomName);

  @override
  FriendInviteResponseModel copyWith({
    UserEntity? user,
    String? roomName,
  }) =>
      FriendInviteResponseModel(
        user: user ?? this.user,
        roomName: roomName ?? this.roomName,
      );

  factory FriendInviteResponseModel.fromRawJson(String str) =>
      FriendInviteResponseModel.fromJson(json.decode(str));

  @override
  String toRawJson() => json.encode(toJson());

  factory FriendInviteResponseModel.fromJson(Map<String, dynamic> json) =>
      FriendInviteResponseModel(
        user: UserEntity.fromJson(json["user"]),
        roomName: json["room_name"],
      );

  @override
  Map<String, dynamic> toJson() => {
        "user": user.toMap(),
        "room_name": roomName,
      };
}
