import 'dart:io';

import 'package:dartz/dartz.dart';
import 'package:play_to_vibe/app/features/user/domain/entities/user_entity.dart';
import 'package:play_to_vibe/app/features/user/domain/entities/user_iq_entity.dart';
import 'package:play_to_vibe/app/features/user/domain/entities/user_login_entity.dart';
import 'package:play_to_vibe/app/features/user/domain/entities/user_login_return_entity.dart';
import 'package:play_to_vibe/core/usecase/errors/failure.dart';

abstract class IUserRepository {
  Future<Either<Failure, UserEntity>> getUserById(int id);
  Future<Either<Failure, int>> getNumberOfRoundsOfLoggedPlayer();
  Future<Either<Failure, UserEntity>> registerUser(UserEntity user);
  Future<Either<Failure, UserLoginResultEntity>> doLogin(
      UserLoginEntity loginData);
  Future<Either<Failure, UserLoginResultEntity>> doLoginWithGoogle(
      UserLoginEntity loginData);
  Future<Either<Failure, UserLoginResultEntity>> doLoginWithApple(
      UserLoginEntity loginData);
  Future<Either<Failure, UserEntity>> uploadUserAvatar(File avatar);
  Future<Either<Failure, UserEntity>> updateUser(UserEntity user);
  Future<Either<Failure, UserEntity>> getUserByFriendUserId(int friendUserId);
  Future<Either<Failure, UserIQEntity>> getUserIQ();
  Future<Either<Failure, bool>> deleteUserAccount(int id);
}
