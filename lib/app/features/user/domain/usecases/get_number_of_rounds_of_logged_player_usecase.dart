// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:dartz/dartz.dart';
import 'package:play_to_vibe/app/features/user/domain/repositories/user_repository.dart';

import 'package:play_to_vibe/core/usecase/errors/failure.dart';
import 'package:play_to_vibe/core/usecase/usecase.dart';

class GetNumberOfRoundsOfLoggedPlayerUseCase
    implements UseCase<int, NoParams?> {
  final IUserRepository repository;

  GetNumberOfRoundsOfLoggedPlayerUseCase(
    this.repository,
  );

  @override
  Future<Either<Failure, int>> call(NoParams? noParams) async {
    return await repository.getNumberOfRoundsOfLoggedPlayer();
  }
}
