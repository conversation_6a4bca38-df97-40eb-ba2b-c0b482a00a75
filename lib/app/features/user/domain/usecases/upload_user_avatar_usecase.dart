import 'dart:io';

import 'package:dartz/dartz.dart';
import 'package:play_to_vibe/app/features/user/domain/repositories/user_repository.dart';

import 'package:play_to_vibe/app/features/user/domain/entities/user_entity.dart';
import 'package:play_to_vibe/core/usecase/errors/failure.dart';
import 'package:play_to_vibe/core/usecase/usecase.dart';

class UploadUserAvatarUseCase implements UseCase<UserEntity, File> {
  final IUserRepository repository;

  UploadUserAvatarUseCase(
    this.repository,
  );

  @override
  Future<Either<Failure, UserEntity>> call(File file) async {
    return await repository.uploadUserAvatar(file);
  }
}
