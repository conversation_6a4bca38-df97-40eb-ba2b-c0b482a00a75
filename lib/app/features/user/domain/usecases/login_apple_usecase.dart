// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:dartz/dartz.dart';
import 'package:play_to_vibe/app/features/user/domain/entities/user_login_entity.dart';
import 'package:play_to_vibe/app/features/user/domain/entities/user_login_return_entity.dart';

import 'package:play_to_vibe/app/features/user/domain/repositories/user_repository.dart';
import 'package:play_to_vibe/core/usecase/errors/failure.dart';
import 'package:play_to_vibe/core/usecase/usecase.dart';

class LoginAppleUseCase
    implements UseCase<UserLoginResultEntity, UserLoginEntity> {
  final IUserRepository repository;

  LoginAppleUseCase(
    this.repository,
  );

  @override
  Future<Either<Failure, UserLoginResultEntity>> call(
      UserLoginEntity data) async {
    return await repository.doLoginWithApple(data);
  }
}
