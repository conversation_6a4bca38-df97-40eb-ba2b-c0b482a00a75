// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:dartz/dartz.dart';
import 'package:play_to_vibe/app/features/user/domain/entities/user_entity.dart';

import 'package:play_to_vibe/app/features/user/domain/repositories/user_repository.dart';
import 'package:play_to_vibe/core/usecase/errors/failure.dart';
import 'package:play_to_vibe/core/usecase/usecase.dart';

class UpdateUserUseCase implements UseCase<UserEntity, UserEntity> {
  final IUserRepository repository;

  UpdateUserUseCase(
    this.repository,
  );

  @override
  Future<Either<Failure, UserEntity>> call(UserEntity user) async {
    return await repository.updateUser(user);
  }
}
