import 'package:dartz/dartz.dart';
import 'package:play_to_vibe/app/features/user/domain/repositories/user_repository.dart';
import 'package:play_to_vibe/core/usecase/errors/failure.dart';
import 'package:play_to_vibe/core/usecase/usecase.dart';

class DeleteUserAccountUseCase implements UseCase<bool, int> {
  final IUserRepository repository;

  DeleteUserAccountUseCase(
    this.repository,
  );

  @override
  Future<Either<Failure, bool>> call(int id) async {
    return await repository.deleteUserAccount(id);
  }
}
