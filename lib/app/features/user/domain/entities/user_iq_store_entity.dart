// ignore_for_file: public_member_api_docs, sort_constructors_first

import 'dart:convert';

import 'package:equatable/equatable.dart';

import 'package:play_to_vibe/app/features/user/domain/entities/user_iq_entity.dart';

class UserIQStoreEntity extends Equatable {
  final UserIQEntity iq;
  final int orderedBy;
  final int xMax;
  final int xMin;

  const UserIQStoreEntity({
    this.iq = const UserIQEntity(info: [], progress: 0),
    this.orderedBy = -1,
    this.xMin = 0,
    this.xMax = 0,
  });

  UserIQStoreEntity copyWith({
    UserIQEntity? iq,
    int? orderedBy,
    int? xMin,
    int? xMax,
  }) {
    return UserIQStoreEntity(
      iq: iq ?? this.iq,
      orderedBy: orderedBy ?? this.orderedBy,
      xMin: xMin ?? this.xMin,
      xMax: xMax ?? this.xMax,
    );
  }

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'iq': iq.toMap(),
      'orderedBy': orderedBy,
      'xMin': xMin,
      'xMax': xMax,
    };
  }

  factory UserIQStoreEntity.fromMap(Map<String, dynamic> map) {
    return UserIQStoreEntity(
      iq: UserIQEntity.fromMap(map['iq']),
      orderedBy: map['orderedBy'] as int,
      xMin: map['xMin'] as int,
      xMax: map['xMax'] as int,
    );
  }

  String toJson() => json.encode(toMap());

  factory UserIQStoreEntity.fromJson(String source) =>
      UserIQStoreEntity.fromMap(json.decode(source) as Map<String, dynamic>);

  @override
  bool get stringify => true;

  @override
  List<Object> get props => [iq, orderedBy, xMin, xMax];
}
