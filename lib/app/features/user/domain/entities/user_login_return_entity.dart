import 'package:equatable/equatable.dart';
import 'package:play_to_vibe/app/features/user/domain/entities/user_entity.dart';

class UserLoginResultEntity extends Equatable {
  final UserEntity user;
  final String token;
  final List<dynamic>? blacklist;
  final bool isReview;
  final bool isGreaterThan18;

  const UserLoginResultEntity({
    required this.user,
    required this.token,
    this.blacklist,
    required this.isReview,
    this.isGreaterThan18 = false,
  });

  UserLoginResultEntity copyWith(
          {UserEntity? user,
          String? token,
          List<dynamic>? blacklist,
          bool? isReview,
          bool? isGreaterThan18}) =>
      UserLoginResultEntity(
          user: user ?? this.user,
          token: token ?? this.token,
          blacklist: blacklist ?? this.blacklist,
          isReview: isReview ?? this.isReview,
          isGreaterThan18: isGreaterThan18 ?? this.isGreaterThan18);

  @override
  List<Object> get props => [
        user,
        token,
        isReview,
      ];
}
