// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'dart:convert';

import 'package:equatable/equatable.dart';

import 'package:play_to_vibe/app/features/user/domain/entities/tag_entity.dart';
import 'package:play_to_vibe/core/utils/type_converters.dart';

class UserIqInfoEntity extends Equatable {
  final double stat;
  final double score;
  final bool isUnlocked;
  final TagEntity tag;
  final int total;
  const UserIqInfoEntity({
    required this.stat,
    required this.score,
    required this.isUnlocked,
    required this.tag,
    required this.total,
  });

  UserIqInfoEntity copyWith({
    double? stat,
    double? score,
    bool? isUnlocked,
    TagEntity? tag,
    int? total,
  }) {
    return UserIqInfoEntity(
      stat: stat ?? this.stat,
      score: score ?? this.score,
      isUnlocked: isUnlocked ?? this.isUnlocked,
      tag: tag ?? this.tag,
      total: total ?? this.total,
    );
  }

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'stat': stat,
      'score': score,
      'isUnlocked': isUnlocked,
      'tag': tag.toMap(),
      'total': total,
    };
  }

  factory UserIqInfoEntity.fromMap(Map<String, dynamic> map) {
    try {
      return UserIqInfoEntity(
        stat: dynamicToDouble(map['stat']),
        score: dynamicToDouble(map['score']),
        isUnlocked: map['is_unlocked'] as bool,
        tag: TagEntity.fromMap(map['tag'] as Map<String, dynamic>),
        total: dynamicToInt(map['total']),
      );
    } catch (err) {
      print('uIQE=>$err');
    }
    return UserIqInfoEntity(
        stat: 0,
        score: 0,
        isUnlocked: true,
        tag: TagEntity.fromMap(map['tag'] as Map<String, dynamic>),
        total: 0);
  }

  String toJson() => json.encode(toMap());

  factory UserIqInfoEntity.fromJson(String source) =>
      UserIqInfoEntity.fromMap(json.decode(source) as Map<String, dynamic>);

  @override
  bool get stringify => true;

  @override
  List<Object> get props {
    return [
      stat,
      score,
      isUnlocked,
      tag,
      total,
    ];
  }
}

class UserIQEntity extends Equatable {
  final double progress;
  final List<UserIqInfoEntity> info;

  const UserIQEntity({
    required this.progress,
    required this.info,
  });

  UserIQEntity copyWith({
    double? progress,
    List<UserIqInfoEntity>? info,
  }) {
    return UserIQEntity(
      progress: progress ?? this.progress,
      info: info ?? this.info,
    );
  }

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'progress': progress,
      'info': info.map((x) => x.toMap()).toList(),
    };
  }

  factory UserIQEntity.fromMap(Map<String, dynamic> map) {
    return UserIQEntity(
      progress: map['progress'] as double,

      info: List<UserIqInfoEntity>.from(map["info"]
          .map((x) => UserIqInfoEntity.fromMap(x as Map<String, dynamic>))),
      // info: List<UserIqInfoEntity>.from(
      //   (map['info'] as List<dynamic>).map(
      //     (x) => UserIqInfoEntity.fromMap(x as Map<String, dynamic>),
      //   ),
      // ),
    );
  }

  String toJson() => json.encode(toMap());

  factory UserIQEntity.fromJson(String source) =>
      UserIQEntity.fromMap(json.decode(source) as Map<String, dynamic>);

  @override
  bool get stringify => true;

  @override
  List<Object> get props => [progress, info];
}
