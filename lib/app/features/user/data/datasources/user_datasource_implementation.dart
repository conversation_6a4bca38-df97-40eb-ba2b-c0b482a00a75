import 'dart:io';
import 'dart:math';

import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:play_to_vibe/app/features/user/data/datasources/endpoints/user_endpoints.dart';
import 'package:play_to_vibe/app/features/user/data/datasources/user_datasource.dart';
import 'package:play_to_vibe/app/features/user/data/models/user_iq_model.dart';
import 'package:play_to_vibe/app/features/user/data/models/user_login_model.dart';
import 'package:play_to_vibe/app/features/user/data/models/user_login_result_model.dart';
import 'package:play_to_vibe/app/features/user/data/models/user_model.dart';
import 'package:play_to_vibe/core/http_client/http_client.dart';
import 'package:play_to_vibe/core/usecase/errors/exceptions.dart';
import 'package:play_to_vibe/core/http_client/http_response.dart' as HttpResp;

class UserDatasourceImplementation implements IUserDatasource {
  final HttpClient client;

  UserDatasourceImplementation(this.client);

  @override
  Future<int> getNumberOfRoundsOfLoggedPlayer() async {
    final response =
        await client.get(UserEndpoints.userGetNumberOfRoundsOfLoggedPlayer());
    if (response.statusCode == 200) {
      int v = 0;
      if (response.data['quantity'] is! int) {
        return int.tryParse(response.data['quantity']) ?? 0;
      }
      return response.data['quantity'];
    }
    if (response.statusCode == 401) {
      throw UnauthorizedException();
    }
    throw ServerException();
  }

  @override
  Future<UserLoginResultModel> doLogin(UserLoginModel loginData) async {
    final response =
        await client.post(UserEndpoints.userLogin(), body: loginData.toJson());
    if (response.statusCode == 200) {
      return UserLoginResultModel.fromJson(response.data);
    }
    if (response.statusCode == 401) {
      throw UnauthorizedException();
    }
    if (response.statusCode == 403) {
      throw SuspendedAccountException();
    }
    throw ServerException();
  }

  @override
  Future<UserLoginResultModel> doLoginWithApple(
      UserLoginModel loginData) async {
    final response = await client.post(UserEndpoints.userAppleLogin(),
        body: {"id": loginData.id, "onesignal_id": loginData.onesignalId});
    if (response.statusCode == 200) {
      return UserLoginResultModel.fromJson(response.data);
    }
    if (response.statusCode == 401) {
      throw UnauthorizedException();
    }
    if (response.statusCode == 403) {
      throw SuspendedAccountException();
    }
    throw ServerException();
  }

  @override
  Future<UserLoginResultModel> doLoginWithGoogle(
      UserLoginModel loginData) async {
    final response = await client.post(UserEndpoints.userGoogleLogin(),
        body: {"id": loginData.id, "onesignal_id": loginData.onesignalId});
    if (response.statusCode == 200) {
      return UserLoginResultModel.fromJson(response.data);
    }
    if (response.statusCode == 401) {
      throw UnauthorizedException();
    }
    if (response.statusCode == 403) {
      throw SuspendedAccountException();
    }
    throw ServerException();
  }

  @override
  Future<UserModel> getUserByFriendUserId(int friendUserId) {
    // TODO: implement getUserByFriendUserId
    throw UnimplementedError();
  }

  @override
  Future<UserModel> getUserById(int id) async {
    final response = await client.get(UserEndpoints.getUserById(id));
    if (response.statusCode == 200) {
      return UserModel.fromJson(response.data);
    }
    if (response.statusCode == 401) {
      throw UnauthorizedException();
    }
    throw ServerException();
  }

  @override
  Future<UserModel> registerUser(UserModel user) async {
    try {
      final response = await client.post(UserEndpoints.register(),
          authenticated: false, body: user.toJson());
      if (response.statusCode == 200) {
        return UserModel.fromJson(response.data);
      }
    } catch (e) {
      throw ServerException();
    }
    throw ServerException();
  }

  @override
  Future<UserModel> updateUser(UserModel user) async {
    final response = await client.put(UserEndpoints.update(user.id),
        authenticated: true, body: user.toJson());
    if (response.statusCode == 200) {
      return UserModel.fromJson(response.data);
    }
    throw ServerException();
  }

  @override
  Future<UserModel> uploadUserAvatar(File avatar) async {
    String fileName = avatar.path.split('/').last;
    FormData formData = FormData.fromMap({
      'avatar': await MultipartFile.fromFile(avatar.path, filename: fileName)
    });

    final response = await client.post(UserEndpoints.uploadAvatar(),
        headers: {'Content-Type': 'multipart/form-data"'}, body: formData);
    if (response.statusCode == 200) {
      return UserModel.fromJson(response.data);
    }
    throw ServerException();
  }

  @override
  Future<UserIQModel> getUserIQ() async {
    final response = await client.get(UserEndpoints.getIQ());
    try {
      if (response.statusCode == 200) {
        return UserIQModel.fromMap(response.data as Map<String, dynamic>);
      }
    } catch (err) {
      debugPrint(':==> $err');
      throw ServerException();
    }
    throw ServerException();
  }

  @override
  Future<bool> deleteUserAccount(int id) async {
    final response = await client.delete(UserEndpoints.deleteUserAccount(id));
    if (response.statusCode == 200) {
      return true;
    } else if (response.statusCode == 400) {
      return false;
    }
    throw ServerException();
  }
}
