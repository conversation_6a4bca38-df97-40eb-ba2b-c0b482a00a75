// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'dart:convert';

import 'package:play_to_vibe/app/features/user/domain/entities/tag_entity.dart';
import 'package:play_to_vibe/core/utils/type_converters.dart';

class TagModel extends TagEntity {
  const TagModel({
    cod,
    title,
    description,
    vibecheckDescription,
    createdAt,
    updatedAt,
  }) : super(
          cod: cod,
          title: title,
          description: description,
          vibecheckDescription: vibecheckDescription,
          createdAt: createdAt,
          updatedAt: updatedAt,
        );

  factory TagModel.fromMap(Map<String, dynamic> map) {
    var olderDate = DateTime.fromMillisecondsSinceEpoch(1);
    return TagModel(
      cod: map['cod'] != null ? map['cod'] as String : '',
      title: map['title'] != null ? map['title'] as String : '',
      description:
          map['description'] != null ? map['description'] as String : '',
      vibecheckDescription: dynamicToString(map['vibecheck_description']),
      createdAt: map['created_at'].runtimeType != Null
          ? DateTime.parse(map['created_at'])
          : olderDate,
      updatedAt: map['updated_at'].runtimeType != Null
          ? DateTime.parse(map['updated_at'])
          : olderDate,
    );
  }

  factory TagModel.fromJson(String source) =>
      TagModel.fromMap(json.decode(source) as Map<String, dynamic>);

  @override
  List<Object> get props {
    return [
      cod,
      title,
      description,
      vibecheckDescription,
      createdAt,
      updatedAt,
    ];
  }
}
