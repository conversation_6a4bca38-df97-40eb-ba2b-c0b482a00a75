// To parse this JSON data, do
//
//     final UserLoginResultModel = UserLoginResultModelFromJson(jsonString);

import 'dart:convert';

import 'package:play_to_vibe/app/features/user/data/models/user_model.dart';
import 'package:play_to_vibe/app/features/user/domain/entities/user_login_return_entity.dart';

class UserLoginResultModel extends UserLoginResultEntity {
  const UserLoginResultModel(
      {required user, required token, blacklist, required isReview})
      : super(
            user: user, token: token, blacklist: blacklist, isReview: isReview);

  factory UserLoginResultModel.fromRawJson(String str) =>
      UserLoginResultModel.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory UserLoginResultModel.fromJson(Map<String, dynamic> json) =>
      UserLoginResultModel(
        user: UserModel.fromJson(json["user"]),
        token: json["token"],
        blacklist: json["blacklist"],
        isReview: json["is_review"] ?? false,
      );

  Map<String, dynamic> toJson() => {
        "user": user,
        "token": token,
        "blacklist": blacklist,
        "is_review": isReview,
      };
}
