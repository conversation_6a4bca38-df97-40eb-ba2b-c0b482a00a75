import 'package:flutter_triple/flutter_triple.dart';
import 'package:play_to_vibe/app/features/user/domain/entities/user_profile_store_entity.dart';
import 'package:play_to_vibe/core/usecase/errors/failure.dart';

class UserProfileStore extends Store<UserProfileStoreEntity> {
  UserProfileStore() : super(const UserProfileStoreEntity());

  setshowSettings(bool newValue) {
    update(state.copyWith(showSettings: newValue), force: true);
  }

  setPinnedTab(bool newValue) {
    update(state.copyWith(pinnedTab: newValue), force: true);
  }
}
