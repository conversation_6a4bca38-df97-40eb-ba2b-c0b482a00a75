import 'dart:math';
import 'dart:ui';

import 'package:play_to_vibe/app/features/user/domain/entities/tag_entity.dart';
import 'package:play_to_vibe/app/features/user/domain/entities/user_iq_entity.dart';
import 'package:play_to_vibe/app/features/user/domain/usecases/get_user_iq_usecase.dart';
import 'package:play_to_vibe/app/features/user/presenter/store/user_iq_store.dart';
import 'package:play_to_vibe/app/features/user/presenter/store/user_profile_store.dart';
import 'package:play_to_vibe/app/features/user/presenter/store/user_store.dart';
import 'package:play_to_vibe/app/theme/colors.dart';
import 'package:play_to_vibe/core/usecase/usecase.dart';

// ignore: must_be_immutable
class UserProfileController {
  final UserStore _userStore;
  final UserProfileStore _userProfileStore;
  final UserIQStore _iqStore;
  final GetUserIQUseCase _getUserIQUseCase;

  UserProfileController(this._userStore, this._iqStore, this._userProfileStore,
      this._getUserIQUseCase);

  UserIQEntity generateTagsMockData() {
    var infoDataMck = <UserIqInfoEntity>[];
    // var data = <ChartSampleData>[];
    // double v = 0;
    Random getRandom = Random.secure();
    String title = '';
    late Color color;
    List<dynamic> value = [];
    int qtd = 8;
    for (int i = 0; i < qtd; i++) {
      value = [
        (qtd - (i + 1)),
        double.parse(((qtd - (i + 1)) / qtd).toStringAsPrecision(2))
      ];
      title = 'Tag -$i';
      color = hexToColor(
          '#${getRandom.nextInt(9)}${getRandom.nextInt(9)}${getRandom.nextInt(9)}${getRandom.nextInt(9)}${getRandom.nextInt(9)}${getRandom.nextInt(9)}');
      // double score = (value + getRandom.nextInt(105));
      infoDataMck.add(UserIqInfoEntity(
          stat: 0,
          score: value[1],
          // score: (score / 100),
          isUnlocked: value[1] > 0,
          tag: TagEntity(
              cod: 'tag00$i',
              title: title,
              createdAt: DateTime.now(),
              updatedAt: DateTime.now(),
              color: color,
              description:
                  'Lorem ipsum dolor sit amet. Et quae veritatis aut inventore perferendis qui sint tempore sit odio recusandae eum magnam rerum et reiciendis animi qui nulla voluptatem. Non dolorum atque non molestiae incidunt et minima neque vel consectetur voluptatem non possimus odio et optio natus ea ratione voluptas. Et enim dignissimos et maiores velit rem fuga quia et quasi saepe est maxime atque et fuga quisquam est enim eius. Id voluptas inventore ex odit sint 33 quisquam laudantium id distinctio laudantium. Ut debitis soluta id officiis quisquam et vitae sapiente est adipisci ipsa sed consequatur alias. Est voluptate tempore est illo reprehenderit ab omnis sunt 33 sunt esse et dolores repudiandae. Sed reprehenderit pariatur qui voluptatem maiores qui quia quidem eos rerum suscipit non omnis quis qui voluptas nihil est quae sequi.',
              vibecheckDescription: ''),
          total: value[0]));
    }

    return UserIQEntity(
        progress: double.parse(getRandom.nextDouble().toStringAsFixed(2)),
        info: infoDataMck);
  }

  Future<void> getIQ() async {
    _userStore.setLoading(true, force: true);

    final result = await _getUserIQUseCase(NoParams());

    result.fold((l) => {print('.$l')}, (r) {
      _iqStore.setIQ(r);
    });
    // _iqStore.setIQ(generateTagsMockData());
    _userStore.setLoading(false, force: true);
    return;
  }

  void toogleshowSettingsState() {
    _userProfileStore.setshowSettings(!_userProfileStore.state.showSettings);
  }
}
