import 'dart:async';
import 'dart:io';

import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:play_to_vibe/analytics.dart';
import 'package:play_to_vibe/app/features/home/<USER>/store/promotion_store.dart';
import 'package:play_to_vibe/app/features/user/domain/entities/user_login_entity.dart';
import 'package:play_to_vibe/app/features/user/domain/entities/user_login_return_entity.dart';
import 'package:play_to_vibe/app/features/user/domain/usecases/delete_user_account_usecase.dart';
import 'package:play_to_vibe/app/features/user/domain/usecases/get_user_by_id_usecase.dart';
import 'package:play_to_vibe/app/features/user/domain/usecases/login_apple_usecase.dart';
import 'package:play_to_vibe/app/features/user/domain/usecases/login_google_usecase.dart';
import 'package:play_to_vibe/app/features/user/presenter/storage/user_storage.dart';
import 'package:play_to_vibe/app/features/user/presenter/store/user_store.dart';
import 'package:play_to_vibe/app/features/user/domain/entities/user_entity.dart';
import 'package:play_to_vibe/app/theme/colors.dart';
import 'package:play_to_vibe/core/services/push_service/abstract_push_service.dart';
import 'package:play_to_vibe/core/services/storage/storage_client.dart';
import 'package:play_to_vibe/core/usecase/errors/failure.dart';
import 'package:play_to_vibe/core/utils/app_routes.dart';
import 'package:play_to_vibe/core/utils/show_dialog_confirmation.dart';
import 'package:play_to_vibe/core/utils/show_dialog_suspended_account_warning.dart';
import 'package:play_to_vibe/core/utils/show_message.dart';
import 'package:play_to_vibe/core/utils/type_converters.dart';
import 'package:play_to_vibe/core/utils/use_cases/get_image_from_url_uint8list_usecase.dart';

// ignore: must_be_immutable
class UserController {
  final StorageClient _storageClient;
  final LoginAppleUseCase _loginAppleUseCase;
  final LoginGoogleUseCase _loginGoogleUseCase;
  final GetUserByIdUseCase _getUserByIdUseCase;
  final GetImageUint8ListFromUrlUseCase _getImageUint8ListFromUrlUseCase;
  final UserStorage _userStorage;
  final UserStore _userStore;
  final DeleteUserAccountUseCase _deleteUserAccountUseCase;
  final userNameTextEditingController = TextEditingController();
  int times = 0;
  String _socialRegisterLoginType = '';

  UserController(
      this._getUserByIdUseCase,
      this._loginAppleUseCase,
      this._loginGoogleUseCase,
      this._userStore,
      this._userStorage,
      this._storageClient,
      this._deleteUserAccountUseCase,
      this._getImageUint8ListFromUrlUseCase);

  String get socialRegisterLoginType => _socialRegisterLoginType;
  set socialRegisterLoginType(String socialLoginType) {
    _socialRegisterLoginType =
        ['apple', 'google'].contains(socialLoginType) ? socialLoginType : '';
  }

  UserEntity getCurrentUser() {
    return _userStore.state.user;
  }

  Future<Uint8List> getImageFromUrlOld(String url) async {
    var completer = Completer<Uint8List>();
    var res = await _getImageUint8ListFromUrlUseCase(url);
    res.fold((l) => completer.completeError(l), (r) => completer.complete(r));
    return completer.future;
  }

  Future<Uint8List> getImageFromUrl(String url) async {
    var completer = Completer<Uint8List>();
    var res = await _getImageUint8ListFromUrlUseCase(url);
    res.fold((l) => completer.completeError(l), (r) => completer.complete(r));
    return completer.future;
  }

  Future<void> updateUserImageFromURL(String url) async {
    getImageFromUrl(url).then((res) async {
      var file = await uint8ListToFile(res);
      _userStore.uploadAvatar(file);
    });
  }

  doLogout({backToWelcomePage = true}) async {
    final PromotionStore promotionStore = Modular.get<PromotionStore>();
    promotionStore.clean();
    await _storageClient.erase();
    await _userStore.cleanStore();

    if (FirebaseAuth.instance.currentUser != null) {
      FirebaseAuth.instance.signOut();
    }
    final pushService = Modular.get<AbstractPushService>();
    pushService.deleteToken();

    if (Modular.to.path != AppRoutes.initialRoute && backToWelcomePage) {
      Modular.to.pushNamedAndRemoveUntil(AppRoutes.initialRoute, (p0) => false);
    }
  }

  setUserLoggedData(UserLoginResultEntity data) async {
    await _userStorage.setUserToken(data.token);
    await _userStorage.setIsReview(data.isReview);
    await _userStorage.setUser(data.user);
    await _userStore.getLoginResultData();
    await _userStorage.setAlreadyPlayed(false);
  }

  Future<bool> doInitialVerification({bool goToHome = true}) async {
    bool res = false;
    await _userStore.getLoginResultData();
    if (_userStore.state.user.id.isNotEmpty) {
      String pushId = await _userStorage.getPushId();
      res = await _userStore.doLogin(UserLoginEntity(
          identifier: _userStore.state.user.identifier ?? '',
          onesignalId: pushId));
      if (!res) {
        if (_userStore.error is SuspendedAccountFailure) {
          ShowDialogSuspendedAccountWarning();
          return false;
        }
      }
      if (goToHome) {
        Modular.to.pushNamedAndRemoveUntil(AppRoutes.home, (p0) => false);
      }
      if (WidgetsBinding.instance.lifecycleState ==
          AppLifecycleState.inactive) {
        res = true;
      } else {
        _userStore.setLoading(true);
        res = await _userStore.doLogin(UserLoginEntity(
            identifier: _userStore.state.user.identifier ?? '',
            onesignalId: pushId));
        if (res == false) {
          doLogout(backToWelcomePage: false);
        } else {
          await _userStore.getLoginResultData();
        }
        _userStore.setLoading(false);
      }
    }
    // if (res && goToHome) {
    //   Modular.to.pushNamedAndRemoveUntil(AppRoutes.home, (p0) => false);
    // }
    return res;
  }

  Future<UserLoginResultEntity?> doLoginWithApple(String identifier) async {
    UserLoginResultEntity? res;
    String oneSignalId = await _userStorage.getPushId();
    _userStore.setLoading(true);
    final result = await _loginAppleUseCase(UserLoginEntity(
        id: identifier, onesignalId: oneSignalId == '' ? oneSignalId : 'noID'));
    result.fold((left) => {_userStore.setError(left)}, (right) {
      setUserLoggedData(right);
      userNameTextEditingController.text = right.user.name;
      res = right;
    });
    _userStore.setLoading(false, force: false);
    return res;
  }

  Future<UserLoginResultEntity?> doLoginWithGoogle(String identifier) async {
    UserLoginResultEntity? res;
    String oneSignalId = await _userStorage.getPushId();
    _userStore.setLoading(true, force: true);
    final result = await _loginGoogleUseCase(UserLoginEntity(
        id: identifier, onesignalId: oneSignalId == '' ? oneSignalId : 'noID'));
    result.fold((left) => {_userStore.setError(left)}, (right) {
      setUserLoggedData(right);
      userNameTextEditingController.text = right.user.name;
      res = right;
    });
    _userStore.setLoading(false, force: true);
    return res;
  }

  Future<UserEntity?> getUserById(String id) async {
    UserEntity? res;
    _userStore.setLoading(true);
    final result = await _getUserByIdUseCase(int.parse(id));
    result.fold((left) => {_userStore.setError(left)}, (right) {
      res = right;
    });
    _userStore.setLoading(false);
    return res;
  }

  Future<bool> doRegisterUser(
      {String name = '',
      File? file,
      String appleId = '',
      String googleId = ''}) async {
    bool res = false;
    String pushId = await _userStorage.getPushId();
    /**
     * if (!kIsWeb && pushId.isEmpty) {
      ShowMessage(
          message:
              'There was a failure trying to register the user, please check your connection and try again later',
          type: MessageType.error);
      return false;
    }
    **/

    UserEntity? userResponse = await _userStore.registerUser(UserEntity(
      name: name,
      onesignalId: pushId,
      googleId: googleId,
      appleId: appleId,
    ));

    try {
      if (userResponse != null) {
        if ((await _userStore.doLogin(UserLoginEntity(
            identifier: _userStore.state.user.identifier ?? '',
            onesignalId: pushId)))) {
          res = true;
          if (file != null) {
            userResponse = await _userStore.uploadAvatar(file);
            await Analytics.instance.logEvent(name: 'register_photo');
          }
        }
        // await _userStorage.setUser(userResponse!);
      }
    } catch (e) {}
    _userStore.setLoading(false);
    return res;
  }

  Future<bool> updateUser(String name,
      {String? appleId, String? googleId}) async {
    googleId = googleId ?? _userStore.state.user.googleId;
    appleId = appleId ?? _userStore.state.user.appleId;
    UserEntity? userResponse = await _userStore.updateUser(_userStore.state.user
        .copyWith(
            name: name,
            appleId: appleId,
            googleId: googleId,
            onesignalId: await _userStorage.getPushId()));
    if (userResponse != null) {
      await _userStorage.setUser(userResponse);
      userNameTextEditingController.text = userResponse.name;
      await _userStore.getLoginResultData();
    }
    return userResponse != null;
  }

  Future<bool> doLoginApple(String name) async {
    UserEntity? userResponse =
        await _userStore.updateUser(_userStore.state.user.copyWith(name: name));
    _userStorage.setUser(userResponse!);
    return userResponse != null;
  }

  Future<void> _onDeleteAccount() async {
    final res =
        await _deleteUserAccountUseCase(int.parse(_userStore.state.user.id));
    res.fold(
        (left) => ShowMessage(
            noAvatar: true,
            message:
                'There was a failure, please check your connection and try again.'),
        (right) {
      ShowDialogConfirmation(
          title: 'WE’LL MISS YOU!',
          text:
              'We are always working to improve, so hopefully you’ll come back soon.',
          buttonText: 'GOODBYE',
          textColor: CustomColors.primary,
          titleColor: CustomColors.primary,
          showCloseButton: false,
          onOk: doLogout);
    });
  }

  Future<void> deleteUserAccount() async {
    ShowDialogConfirmation(
      title: 'ARE YOU SURE?',
      text:
          'Deleting your account permanently all of your progress and information.',
      buttonText: 'DELETE ACCOUNT',
      textColor: CustomColors.primary,
      titleColor: CustomColors.primary,
      showCancelButton: true,
      buttonCancelText: 'Cancel',
      onOk: _onDeleteAccount,
    );
  }
}
