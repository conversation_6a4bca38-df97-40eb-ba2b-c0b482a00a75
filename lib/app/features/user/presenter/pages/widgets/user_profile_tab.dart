import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:play_to_vibe/analytics.dart';
import 'package:play_to_vibe/app/theme/colors.dart';
import 'package:sizer/sizer.dart';

class UserProfileTab extends StatefulWidget {
  final List<String> tabs;
  final List<Widget> children;
  final bool useController;
  const UserProfileTab(
      {super.key,
      this.tabs = const [],
      this.children = const [],
      this.useController = true});

  @override
  _UserProfileTabState createState() => _UserProfileTabState();
}

class _UserProfileTabState extends State<UserProfileTab>
    with SingleTickerProviderStateMixin {
  late final int length;
  late List<Tab> tabs;
  late final TabController? _tabController;

  @override
  void initState() {
    super.initState();
    length = widget.tabs.length;
    tabs = [];
    for (var tab in widget.tabs) {
      tabs.add(Tab(
        text: tab,
      ));
    }
    _tabController = widget.useController
        ? TabController(length: length, vsync: this)
        : null;
  }

  Future<void> _logOpenTopVibesEvent() async {
    Analytics.instance.logEvent(name: 'top_vibes_click');
  }

  Future<void> _logOpenPlayerHistoryEvent() async {
    Analytics.instance.logEvent(name: 'player_history_click');
  }

  @override
  void dispose() {
    _tabController?.dispose();
    super.dispose();
  }

  void _handleTabChange(int index) {
    if (index == 1) {
      _logOpenTopVibesEvent();
    } else if (index == 2) {
      _logOpenPlayerHistoryEvent();
    }
  }

  Widget getTab() {
    return Container(
      decoration: const BoxDecoration(
          color: Color.fromRGBO(248, 248, 248, 1),
          borderRadius: BorderRadius.all(Radius.circular(30))),
      // margin: const EdgeInsets.symmetric(horizontal: 10.0),
      width: 83.34.w,
      height: 4.73.h,
      child: TabBar(
        controller: _tabController,
        indicatorSize: TabBarIndicatorSize.tab,
        onTap: _handleTabChange,
        unselectedLabelColor: const Color.fromRGBO(210, 210, 210, 1),
        dividerColor: Colors.transparent,
        labelStyle: const TextStyle(
            letterSpacing: 1.95,
            fontFamily: "Roboto",
            fontWeight: FontWeight.bold,
            color: Colors.white,
            fontSize: 13),
        indicator: BoxDecoration(
            borderRadius: BorderRadius.circular(50),
            color: CustomColors.orangeSoda),
        tabs: tabs,
      ),
    );
  }

  Widget getTabView() {
    return Expanded(
      child: TabBarView(controller: _tabController, children: widget.children),
    );
  }

  @override
  Widget build(BuildContext context) {
    return getTab();
  }
}
