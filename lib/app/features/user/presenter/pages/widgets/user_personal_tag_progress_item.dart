import 'package:flutter/material.dart';
import 'package:play_to_vibe/app/features/user/domain/entities/user_iq_entity.dart';
import 'package:play_to_vibe/app/widgets/custom_image.dart';
import 'package:play_to_vibe/core/utils/assets_strings.dart';
import 'package:play_to_vibe/core/utils/type_converters.dart';
import 'package:sizer/sizer.dart';

class UserPersonalTagProgressItem extends StatelessWidget {
  final UserIqInfoEntity data;

  const UserPersonalTagProgressItem({super.key, required this.data});

  Widget getBar(Color color) {
    return data.isUnlocked
        ? data.stat > 0
            ? Container(
                width: data.stat * 195,
                constraints: const BoxConstraints(
                    minWidth: 29.0), // * data.tag.title.length),
                margin: const EdgeInsets.symmetric(horizontal: 10),
                height: 30,
                decoration: BoxDecoration(
                    color: color,
                    borderRadius: data.stat > 0
                        ? const BorderRadius.only(
                            topLeft: Radius.circular(6),
                            bottomLeft: Radius.circular(6),
                            topRight: Radius.circular(28),
                            bottomRight: Radius.circular(28))
                        : null),
              )
            : Container(
                margin: const EdgeInsets.symmetric(horizontal: 10),
                height: 30,
                width: 8,
                decoration: BoxDecoration(
                  color: color,
                ),
              )
        : const SizedBox();
  }

  Widget getPercentageWidget(Color color) {
    return data.isUnlocked
        ? Text(
            '${decimalToPercentageConverter(data.stat)}%',
            style: TextStyle(
                letterSpacing: 1.3,
                fontFamily: "Roboto",
                fontWeight: FontWeight.w700,
                color: color,
                fontSize: 13),
          )
        : const SizedBox();
  }

  Widget getBarContainer(Color color) {
    String title = ''; //data.tag.title;
    BoxConstraints? constraints =
        const BoxConstraints(minWidth: 29); //* data.tag.title.length);
    double? width = data.stat * 195;
    EdgeInsets? margin;
    double leftPadding = 25;
    double textSize = 13;
    double topPadding = 0;
    FontWeight fontWeight = FontWeight.w700;

    if (!data.isUnlocked) {
      // textSize = 12;
      title = '?';
      leftPadding = 15;
      topPadding = 12;
      fontWeight = FontWeight.w500;
    }

    final Text text = Text(title,
        style: TextStyle(
            letterSpacing: 1.3,
            fontFamily: "Roboto",
            fontWeight: fontWeight,
            color: data.stat > 0 ? Colors.white : color,
            fontSize: textSize));

    Widget textChild = text;

    if (data.stat <= 0) {
      width = null;
      constraints = null;
      margin = const EdgeInsets.only(right: 8);
      textChild = Padding(
          padding: EdgeInsets.only(left: leftPadding, top: topPadding),
          child: text);
    }

    return Container(
        margin: margin,
        width: width, //data.stat * 185,
        constraints:
            constraints, //BoxConstraints(minWidth: 13.0 * title.length),
        child: Stack(alignment: Alignment.centerLeft, children: [
          getBar(color),
          Align(
            alignment: Alignment.center,
            child: textChild,
          )
        ]));
  }

  String getCorrectIconName(String iconName) {
    switch (iconName) {
      case 'classic':
        return 'cl';
      case 'knk':
        return 'kin';
      default:
        return iconName;
    }
  }

  Widget _buildIcon(String imagePath, String fallbackImageName) {
    return SizedBox(
      height: 4.h,
      child: CustomImage(
        imagePath,
        defaultImagePath: Assets.svg.iq[fallbackImageName],
        fit: BoxFit.fitHeight,
      ),
    );
  }

  Widget getIconSection(Color color) {
    String imagePath = data.tag.imageUrl;
    String fallbackImage = getCorrectIconName(data.tag.icon);
    bool withShadow = false;
    Widget tagNameTextWidget = SizedBox(
      height: 12,
    );
    // Text(
    //   data.tag.cod.toUpperCase(),
    //   style: TextStyle(
    //     fontFamily: 'Roboto',
    //     fontSize: 13,
    //     color: color,
    //     letterSpacing: 1.3,
    //     fontWeight: FontWeight.w500,
    //   ),
    // );
    if (!data.isUnlocked) {
      withShadow = true;
      imagePath = '';
      fallbackImage = 'disabled';
      // return Padding(
      //   padding: const EdgeInsets.only(top: 12.0),
      //   child: CircleAvatar(
      //     radius: 15,
      //     backgroundColor: color,
      //   ),
      // );
    }
    return SizedBox(
      width: 4.h,
      child: Column(
        children: [
          SizedBox(
            height: 1.42.h,
          ),
          _buildIcon(imagePath, fallbackImage),
          tagNameTextWidget,
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    Color color = data.isUnlocked ? data.tag.color : Colors.grey;
    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 12.0),
          child: Row(
            children: [
              getIconSection(color),
              getBarContainer(color),
              getPercentageWidget(color),
            ],
          ),
        )
      ],
    );
  }
}
