import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:play_to_vibe/app/features/user/domain/entities/user_iq_entity.dart';
import 'package:play_to_vibe/app/theme/colors.dart';
import 'package:play_to_vibe/app/widgets/button.dart';
import 'package:play_to_vibe/app/widgets/custom_image.dart';
import 'package:play_to_vibe/core/utils/assets_strings.dart';
import 'package:play_to_vibe/core/utils/quycky_icons_icons.dart';

class UserStageTagDetailCard extends StatelessWidget {
  final void Function()? onTapPlayToUnlock;
  final UserIqInfoEntity data;
  late final Color color;
  final EdgeInsets? margin;

  UserStageTagDetailCard(
      {super.key, this.margin, this.onTapPlayToUnlock, required this.data}) {
    color = data.tag.color;
  }

  Widget getCardButton() {
    void Function()? func;
    return SizedBox(
      height: 30,
      width: 170,
      child: Button(
          borderColor: Colors.white,
          outlined: true,
          fontSize: 13,
          autoSized: true,
          color: Colors.white,
          textColor: Colors.white,
          text: 'COMING SOON',
          onPressed: func),
    );
  }

  Widget getDescriptionWidget() {
    final description = data.tag.description.length > 220
        ? '${data.tag.description.substring(0, 220)}...'
        : data.tag.description;
    Widget t = RichText(
        textAlign: TextAlign.center,
        text: TextSpan(
          text: description,
          style: const TextStyle(
              letterSpacing: 0.18,
              height: 1,
              fontFamily: "Roboto",
              fontWeight: FontWeight.w400,
              color: Colors.white,
              fontSize: 13),
        ));
    return t;
  }

  void onTap() {
    if (!data.isUnlocked) {
      if (onTapPlayToUnlock != null) {
        onTapPlayToUnlock!();
      }
    }
  }

  Widget getCardChild() {
    List<Widget> columnChildren = <Widget>[
      const SizedBox(
        height: 32,
      ),
      Text(
        data.tag.title.toUpperCase(),
        overflow: TextOverflow.ellipsis,
        style: const TextStyle(
            letterSpacing: 1.8,
            fontFamily: "Roboto",
            fontWeight: FontWeight.w700,
            color: Colors.white,
            fontSize: 18),
      ),
      Padding(
        padding: const EdgeInsets.only(top: 8.0),
        child: getDescriptionWidget(),
      ),
    ];

    if (data.isUnlocked) {
      columnChildren.addAll([
        const Spacer(),
        getCardButton(),
        const SizedBox(height: 12),
      ]);
      return Column(
        children: columnChildren,
      );
    }

    return Stack(
      children: [
        ImageFiltered(
          imageFilter: ImageFilter.blur(sigmaX: 8, sigmaY: 8),
          child: Column(
            children: columnChildren,
          ),
        ),
        const Align(
            alignment: Alignment.center,
            child: SizedBox(
              height: 58, //53
              child: Column(
                children: [
                  Icon(Icons.lock_outline, size: 20, color: Colors.white),
                  SizedBox(height: 4),
                  SizedBox(
                    height: 30,
                    child: Button(
                      outlined: true,
                      borderColor: Colors.white,
                      child: Text(
                        'PLAY TO UNLOCK',
                        style: TextStyle(
                            letterSpacing: 1.5,
                            fontFamily: "Roboto",
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                            fontSize: 16),
                      ),
                    ),
                  ),
                ],
              ),
            )),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    final double height = data.isUnlocked ? 185 : 140;
    return GestureDetector(
      onTap: onTap,
      child: Container(
        height: height + 40,
        constraints: const BoxConstraints(
          maxWidth: 325,
        ),
        child: Stack(
          children: [
            Positioned(
              left: 0,
              right: 0,
              bottom: 0,
              child: Container(
                  height: height,
                  margin: margin,
                  padding: const EdgeInsets.symmetric(horizontal: 8),
                  decoration: BoxDecoration(
                      color: color, borderRadius: BorderRadius.circular(20)),
                  child: getCardChild()),
            ),
            Positioned(
              top: 0,
              right: 0,
              left: 0,
              child: SizedBox(
                  width: 50,
                  height: 50,
                  child: CustomImage(data.tag.imageUrl,
                      defaultImagePath:
                          Assets.svg.iq.getByName(data.tag.icon))),
            ),
          ],
        ),
      ),
    );
  }
}
