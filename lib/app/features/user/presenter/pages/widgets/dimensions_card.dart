import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:play_to_vibe/app/features/user/domain/entities/user_iq_entity.dart';
import 'package:play_to_vibe/app/theme/colors.dart';
import 'package:play_to_vibe/app/widgets/custom_image.dart';
import 'package:play_to_vibe/core/utils/assets_strings.dart';
import 'package:sizer/sizer.dart';
import 'package:skeletonizer/skeletonizer.dart';

class DimensionsCard extends StatefulWidget {
  final List<UserIqInfoEntity> iqInfoData;
  final Function(UserIqInfoEntity) onShowDimensionInfo;
  const DimensionsCard(
      {super.key, required this.iqInfoData, required this.onShowDimensionInfo});

  @override
  State<DimensionsCard> createState() => _DimensionsCardState();
}

class _DimensionsCardState extends State<DimensionsCard> {
  final dimensionsOrder = [
    "van",
    "rom",
    "mp",
    "trl",
    "spo",
    "foc",
    "cl",
    "res",
    "spr",
    "kin",
  ];

  void _sortData() {
    if (widget.iqInfoData.isNotEmpty) {
      widget.iqInfoData.sort((a, b) {
        int indexA = dimensionsOrder.indexOf(a.tag.cod);
        int indexB = dimensionsOrder.indexOf(b.tag.cod);
        return indexA.compareTo(indexB);
      });
    }
  }

  @override
  void initState() {
    super.initState();
  }

  Widget getImage(String imagePath, String fallbackImagePath,
      {withShadow = true}) {
    final Widget image = CustomImage(imagePath,
        defaultImagePath: Assets.svg.iq[fallbackImagePath],
        width: 5.82.h,
        height: 5.82.h,
        fit: BoxFit.fitHeight);
    if (withShadow) {
      return Stack(
        children: [
          ImageFiltered(
            imageFilter: ImageFilter.blur(sigmaX: 0.5, sigmaY: 0.5),
            child: ColorFiltered(
              colorFilter:
                  ColorFilter.mode(Colors.black.withAlpha(38), BlendMode.srcIn),
              child: CustomImage(Assets.svg.iq[imagePath],
                  width: 5.83.h, height: 5.83.h, fit: BoxFit.fitHeight),
            ),
          ),
          image,
        ],
      );
    }
    return image;
  }

  List<Widget> _buildDimensionIcons() {
    _sortData();
    return widget.iqInfoData.map((e) {
      String name = e.tag.cod;
      Widget icon = getImage(e.tag.imageUrl, name, withShadow: true);
      if (!e.isUnlocked) {
        name = 'disabled';
        icon = getImage('', name, withShadow: true);
      }

      return GestureDetector(onTap: () => handleOnDimensionTap(e), child: icon);
    }).toList();
  }

  void handleOnDimensionTap(UserIqInfoEntity dimensionInfo) {
    widget.onShowDimensionInfo(dimensionInfo);
  }

  @override
  Widget build(BuildContext context) {
    return Skeletonizer(
      enabled: widget.iqInfoData.isEmpty,
      child: Container(
        width: 83.34.w,
        height: 30.h,
        decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.all(Radius.circular(30))),
        child: Column(children: [
          const Padding(
            padding: EdgeInsets.only(top: 36),
            child: Center(
              child: Text(
                'DIMENSIONS',
                style: TextStyle(
                    letterSpacing: 1,
                    fontFamily: "Roboto",
                    fontWeight: FontWeight.bold,
                    color: CustomColors.americanPurple2,
                    fontSize: 16),
              ),
            ),
          ),
          Padding(
            padding: EdgeInsets.symmetric(vertical: 3.17.h),
            child: Wrap(
              runSpacing: 4.6.w,
              spacing: 1.h,
              direction: Axis.horizontal,
              alignment: WrapAlignment.spaceBetween,
              children: _buildDimensionIcons(),
            ),
          ),
          const Center(
            child: Text(
              'TAP TO GO DEEPER',
              style: TextStyle(
                  letterSpacing: 0.6,
                  fontFamily: "Roboto",
                  fontWeight: FontWeight.w500,
                  color: CustomColors.americanPurple2,
                  fontSize: 12),
            ),
          )
        ]),
      ),
    );
  }
}
