import 'dart:async';
import 'dart:math';

import 'package:flutter/material.dart';
import 'package:graphic/graphic.dart';
import 'package:play_to_vibe/app/features/user/domain/entities/user_iq_entity.dart';
import 'package:play_to_vibe/app/theme/colors.dart';
import 'package:play_to_vibe/app/widgets/avatar.dart';

class UserRadiusChart extends StatelessWidget {
  final List<UserIqInfoEntity> data;
  final double height;
  final double width;
  final String? userAvatar;
  final Function(UserIqInfoEntity) showDimensionDetails;
  final gestureStream = StreamController<Selected>.broadcast();

  UserRadiusChart(
      {this.userAvatar,
      required this.data,
      required this.height,
      required this.width,
      required this.showDimensionDetails,
      super.key}) {
    gestureStream.stream.listen((event) {
      // showDimensionDetails(event.data as UserIqInfoEntity);
      for (var entry in event.entries) {
        if (entry.key == 'tap') {
          showDimensionDetails(data[entry.value.first]);
          break;
        }
      }
    });
  }

  Widget _buildUserRadiusChart(List<UserIqInfoEntity> dataCleaned,
      double innerHeight, double innerWidth, List<Color> palette) {
    return Stack(
      children: [
        // ...getIconsLabels(),
        // const Positioned(bottom: 0, left: 0, child: Icon(Icons.abc)),
        Align(
          alignment: Alignment.center,
          child: SizedBox(
            height: innerHeight,
            width: innerWidth,
            child: Chart<UserIqInfoEntity>(
              data: dataCleaned,
              variables: {
                'name': Variable(
                  accessor: (UserIqInfoEntity map) => map.tag.title,
                ),
                'value': Variable(
                  accessor: (UserIqInfoEntity map) => map.stat,
                  scale: LinearScale(min: 0, marginMax: 0.1),
                ),
              },
              marks: [
                IntervalMark(
                    // label: LabelEncode(
                    //     encoder: (tuple) =>
                    //         Label(tuple['name'].toString())),
                    shape: ShapeEncode(
                        value: RectShape(
                      borderRadius: const BorderRadius.all(Radius.circular(10)),
                    )),
                    color: ColorEncode(variable: 'name', values: palette),
                    elevation: ElevationEncode(value: 5),
                    transition: Transition(
                        duration: const Duration(seconds: 2),
                        curve: Curves.elasticOut),
                    entrance: {MarkEntrance.y},
                    selectionStream: gestureStream)
              ],
              // selections: {'tap': PointSelection(dim: Dim.x)},
              // tooltip: TooltipGuide(),
              // crosshair: CrosshairGuide(),

              selections: {
                'tap': PointSelection(
                  on: {
                    GestureType.hover,
                    GestureType.tap,
                  },
                  dim: Dim.x,
                )
              },
              coord: PolarCoord(startRadius: 0.48),
              axes: [
                Defaults.circularAxis..label = null,
                // Defaults.radialAxis..label = null,
                // Defaults.radialAxis..labelMapper = null,
              ],
            ),
          ),
        ),
        Align(
          alignment: Alignment.center,
          child: Avatar(
            size: height / 3,
            addPhotoButton: false,
            imagePath: userAvatar,
          ),
        ),
        // Align( //Icons
        //     alignment: Alignment.center,
        //     child: SizedBox(
        //       height: innerHeight + 38,
        //       width: innerWidth + 38,
        //       child: CircleIconsWidget(
        //           iconSize: 30,
        //           colors: palette,
        //           circleRadius: innerWidth / 2,
        //           numberOfIcons: dataCleaned.length),
        //     )),
      ],
    );
  }

  Widget _buildHasNoSufficientData() {
    return const Center(
      child: Text(
        'There is not enough data to create the chart',
        textAlign: TextAlign.center,
        style: TextStyle(
            letterSpacing: 1.2,
            fontFamily: "Roboto",
            fontWeight: FontWeight.bold,
            color: CustomColors.americanPurple,
            fontSize: 28),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    late final double innerHeight;
    late final double innerWidth;
    late final List<Color> palette;
    late final bool canShowChart;

    innerHeight = height;
    // -70;
    innerWidth = width;
    //- 70;
    canShowChart = data.length > 1;
    palette = data.map((v) => v.tag.color).toList();

    return SizedBox(
      height: height,
      width: width,
      child: canShowChart
          ? _buildUserRadiusChart(data, innerHeight, innerWidth, palette)
          : _buildHasNoSufficientData(),
    );
  }
}

class CircleIconsWidget extends StatelessWidget {
  final int numberOfIcons;
  final double circleRadius;
  final double iconSize;
  final List<Color> colors;
  final subtraction = {
    1: -1.56,
    2: 0.0,
    3: 0.55,
    4: 0.78,
    5: 0.95,
    6: 1.05,
    7: 1.12,
    8: 1.18,
    9: 1.23,
    10: 1.28,
  };
  //  = const <Color>[
  //   Colors.black,
  //   Colors.red,
  //   Colors.green,
  //   Colors.blue,
  //   Colors.blueGrey,
  //   Colors.pink,
  //   Colors.lightBlue,
  //   Colors.brown,
  // ];

  CircleIconsWidget(
      {super.key,
      required this.numberOfIcons,
      required this.circleRadius,
      this.iconSize = 30.0,
      required this.colors});

  double getAngle(int index, int totalElements) {
    return (2 * pi * index / totalElements);
  }

  @override
  Widget build(BuildContext context) {
    final subtr = subtraction[numberOfIcons] ?? 0;

    return Stack(
      children: List.generate(
        numberOfIcons,
        (index) {
          double angle = getAngle(index, numberOfIcons);
          angle -= subtr;

          double x = circleRadius * cos(angle);
          double y = circleRadius * sin(angle);
          double vSum = x > -0.3 ? 10 : 4;
          x += vSum * cos(angle);
          y += vSum * sin(angle);

          return Positioned(
              left: x + circleRadius,
              top: y + circleRadius,
              child: Icon(Icons.star, size: iconSize, color: colors[index]));
        },
      ),
    );
  }
}
