import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:play_to_vibe/app/features/user/presenter/pages/state/user_profile_iq_state.dart';
import 'package:play_to_vibe/app/services/remote_config/remote_config_store.dart';

class UserProfilePage extends StatefulWidget {
  final String title;
  late final State<UserProfilePage> _pageState;

  UserProfilePage({super.key, this.title = 'UserProfilePage'}) {
    final remoteConfig = Modular.get<RemoteConfigStore>();
    _pageState = UserProfileIQState();
  }

  @override
  State<UserProfilePage> createState() => _pageState;
}
