import 'dart:io';
import 'dart:typed_data';

import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter_svg/svg.dart';
import 'package:flutter_triple/flutter_triple.dart';
import 'package:image_picker/image_picker.dart';
import 'package:play_to_vibe/analytics.dart';
import 'package:play_to_vibe/app/features/user/domain/entities/user_login_return_entity.dart';
import 'package:play_to_vibe/app/features/user/presenter/controllers/user_controller.dart';
import 'package:play_to_vibe/app/features/user/presenter/store/user_store.dart';
import 'package:play_to_vibe/app/widgets/gradient_container.dart';
import 'package:play_to_vibe/core/utils/app_routes.dart';
import 'package:play_to_vibe/core/utils/assets_strings.dart';
import 'package:play_to_vibe/core/utils/get_image_from.dart';
import 'package:play_to_vibe/core/utils/quycky_icons_icons.dart';
import 'package:play_to_vibe/core/utils/show_dialog_confirmation.dart';
import 'package:play_to_vibe/core/utils/show_dialog_get_image.dart';
import 'package:play_to_vibe/core/utils/show_message.dart';
import 'package:play_to_vibe/core/utils/social_sign_in.dart';
import 'package:play_to_vibe/core/utils/text_formatter/upper_case_text_formatter.dart';
import 'package:play_to_vibe/app/widgets/avatar.dart';
import 'package:play_to_vibe/app/widgets/button.dart';
import 'package:play_to_vibe/app/theme/colors.dart';
import 'package:play_to_vibe/app/widgets/quycky_modal_progress.dart';
import 'package:play_to_vibe/core/usecase/errors/failure.dart';
import 'package:play_to_vibe/core/utils/type_converters.dart';

class SocialLoginResultEntity {
  UserCredential? userCredential;
  UserLoginResultEntity? loginResult;
  SocialLoginResultEntity({this.userCredential, this.loginResult});
}

class UserRegisterPage extends StatefulWidget {
  final String title;

  const UserRegisterPage({super.key, this.title = 'UserRegisterPage'});

  @override
  UserRegisterPageState createState() => UserRegisterPageState();
}

class UserRegisterPageState extends State<UserRegisterPage> {
  XFile? image;
  bool isGreaterThan18 = false;
  final PickImage _pickImage = PickImage();
  final SocialSignIn _socialSignIn = SocialSignIn();
  final _controller = Modular.get<UserController>();
  final _userStore = Modular.get<UserStore>();
  String googleId = '';
  String appleId = '';

  TextEditingController userNameTextEditController = TextEditingController();
  UserRegisterPageState() {
    if (_controller.socialRegisterLoginType.isNotEmpty) {
      handleSocialSignIn(
          tryGoogle: _controller.socialRegisterLoginType == 'google');
      _controller.socialRegisterLoginType = '';
    }
  }

  @override
  void initState() {
    super.initState();
    userNameTextEditController.text = '';
    _userStore.SetError(const GenericFailure(message: 'CHOOSE A NAME!'));
  }

  Widget getButton() {
    return Button(
      onPressed: handleSave,
      text: "Play",
    );
  }

  void handleSetImage(XFile? file) async {
    image = file;
    _userStore.setAvatarUrl(file?.path);
  }

  void getImageFrom() async {
    // XFile? image = await _pickImage.getImageFrom(camera: false);
    // _userStore.setAvatarUrl(image!.path);
    ShowDialogGetImage(onOk: handleSetImage);
  }

  Future<SocialLoginResultEntity> tryGoogleSignIn() async {
    UserCredential? signInResult = await _socialSignIn.signInWithGoogle();
    googleId = signInResult?.user?.uid ?? '';
    // print('=================================>');
    // print('==GoogleId:=>$googleId');
    // print('<==================================');
    final res = await _controller.doLoginWithGoogle(googleId);
    return SocialLoginResultEntity(
        userCredential: signInResult, loginResult: res);
  }

  Future<SocialLoginResultEntity> tryAppleSingIn() async {
    UserCredential signInResult = await _socialSignIn.signInWithApple();
    appleId = signInResult.user?.uid ?? '';
    // print('=================================>');
    // print('==>$appleId');
    // print('<==================================');
    return SocialLoginResultEntity(
        userCredential: signInResult,
        loginResult: await _controller.doLoginWithApple(appleId));
  }

  void handleSocialSignIn({bool tryGoogle = false}) async {
    SocialLoginResultEntity res;
    try {
      res = await (tryGoogle ? tryGoogleSignIn() : tryAppleSingIn());
      if (res.loginResult != null) {
        _userStore.setData(res.loginResult!);
        handleGoToHome();
        return;
      }
      String userPhoto = res.userCredential?.user?.photoURL! ?? '';
      if (userPhoto.isNotEmpty) {
        _userStore.setAvatarUrl(res.userCredential?.user?.photoURL ?? '');
      }
      userNameTextEditController.text =
          (res.userCredential?.user?.displayName ?? '').toUpperCase();
    } catch (e) {
      print(
          '==>${e.toString() == '[firebase_auth/web-context-canceled] The web operation was canceled by the user.'}');
    }
  }

  // Future<UserCredential> signInWithApple() async {
  //   final appleProvider = AppleAuthProvider();
  //   UserCredential res;
  //   if (kIsWeb) {
  //     res = await FirebaseAuth.instance.signInWithPopup(appleProvider);
  //   } else {
  //     res = await FirebaseAuth.instance.signInWithProvider(appleProvider);
  //   }
  //   print('==>${res.user!.email}');
  //   return res;
  // }

  void showDialogMustBe18YearsOld() {
    ShowDialogConfirmation(
        onOk: () {
          _userStore.setIsGreaterThan18(true);
          handleSave();
        },
        textWidget: const Text(
          'YOU MUST BE AT LEAST 18\nYEARS OLD TO ACCESS THIS APP',
          textAlign: TextAlign.center,
          style: TextStyle(
              fontFamily: 'Roboto',
              fontSize: 12,
              height: 1.5,
              color: CustomColors.americanPurple2,
              letterSpacing: 1,
              fontWeight: FontWeight.bold,
              decoration: TextDecoration.none),
        ),
        buttonText: 'YES, I AM OVER 18 YEARS OLD');
  }

  void handleSave() async {
    // handleGoToHome();
    // return;
    File? file;
    await Analytics.instance.logEvent(name: 'register_play_press');
    try {
      if (userNameTextEditController.text.isEmpty) {
        _userStore.SetError(
            const GenericFailure(message: 'PLEASE CHOOSE A NAME FIRST!'));
        return;
      }
      if (!_userStore.state.isGreaterThan18) {
        showDialogMustBe18YearsOld();
        return;
      }
      if (_userStore.state.user.avatarUrl != null) {
        if (_userStore.state.user.avatarUrl!.startsWith('http')) {
          // var tempUint8List =
          var tempUint8List = await _controller
              .getImageFromUrl(_userStore.state.user.avatarUrl!);
          file = await uint8ListToFile(tempUint8List);
        } else {
          file = File(_userStore.state.user
              .avatarUrl!); //await getUint8ListFromImage(_userStore.state.user.avatarUrl!);
          // File(_userStore.state.user.avatarUrl!);
        }
      }
      if (await _controller.doRegisterUser(
          name: userNameTextEditController.text,
          file: file,
          googleId: googleId,
          appleId: appleId)) {
        await Analytics.instance.logEvent(name: 'register_success');
        handleGoToIntro();
      }
    } catch (e) {
      print('Err:URP=>$e');
      ShowMessage(message: defaultFailureMessage, type: MessageType.error);
    }
  }

  void handleGoToHome() async {
    Modular.to.pushReplacementNamed(AppRoutes.home);
  }

  void handleGoToIntro() async {
    Modular.to.pushReplacementNamed(AppRoutes.tutorial, arguments: true);
  }

  handleOpenTutorial() => Modular.to.pushReplacementNamed(AppRoutes.tutorial);

  @override
  void dispose() {
    _userStore.SetError(const GenericFailure(message: 'CHOOSE A NAME!'));
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: GradientContainer(
        useDefault: true,
        normalOpacity: 0,
        hotOpacity: 0,
        coldOpacity: 0,
        child: TripleBuilder(
            store: _userStore,
            builder: (context, triple) {
              Triple<UserLoginResultEntity> store =
                  triple as Triple<UserLoginResultEntity>;
              return QuyckyModalProgress(
                  state: store.isLoading,
                  child: SafeArea(
                    child: SingleChildScrollView(
                      child: Padding(
                        padding: const EdgeInsets.only(top: 14.0),
                        child: Column(
                          children: [
                            SizedBox(
                                height: 45,
                                child: SvgPicture.asset(
                                    Assets.svg.quyckyLogoWhite)),
                            const Padding(
                              padding: EdgeInsets.only(top: 20.0),
                              child: Text(
                                "PROFILE",
                                style: TextStyle(
                                    letterSpacing: 1.2,
                                    fontFamily: "Roboto",
                                    fontWeight: FontWeight.bold,
                                    color: Colors.white,
                                    fontSize: 12),
                              ),
                            ),
                            const Padding(
                              padding: EdgeInsets.only(top: 15.0),
                              child: Text(
                                "ADD A PHOTO TO YOUR PROFILE",
                                style: TextStyle(
                                    letterSpacing: 1.2,
                                    fontFamily: "Roboto",
                                    fontWeight: FontWeight.bold,
                                    color: Colors.white,
                                    fontSize: 10),
                              ),
                            ),
                            Padding(
                              padding: const EdgeInsets.only(top: 53.0),
                              child: Stack(children: [
                                Avatar(
                                  size: 100,
                                  outlined: true,
                                  addPhotoButton: true,
                                  imagePath: (store.state).user.avatarUrl,
                                  onPressed: getImageFrom,
                                ),
                              ]),
                            ),
                            Padding(
                              padding: const EdgeInsets.only(
                                  left: 30, right: 30, top: 53),
                              child: Container(
                                constraints:
                                    const BoxConstraints(maxWidth: 380),
                                child: Column(
                                  children: [
                                    Container(
                                      height: 50,
                                      decoration: const BoxDecoration(
                                          color: CustomColors.orangeSoda90,
                                          borderRadius: BorderRadius.only(
                                              topLeft: Radius.circular(30),
                                              topRight: Radius.circular(30))),
                                      child: TextFormField(
                                        controller: userNameTextEditController,
                                        inputFormatters: [
                                          UpperCaseTextFormatter()
                                        ],
                                        validator: (value) {
                                          if (value != null &&
                                              value.length < 3) {
                                            return 'a minimum of 3 characters is required';
                                          }
                                          return null;
                                        },
                                        decoration: InputDecoration(
                                          contentPadding:
                                              const EdgeInsets.all(18.0),
                                          border: InputBorder.none,
                                          hintText: store.error?.message ?? '',
                                          hintStyle: const TextStyle(
                                              letterSpacing: 3,
                                              fontFamily: "Roboto",
                                              color: Colors.white,
                                              fontSize: 10),
                                        ),
                                        style: const TextStyle(
                                            letterSpacing: 3,
                                            fontFamily: "Roboto",
                                            color: Colors.white,
                                            fontSize: 10),
                                      ),
                                    ),
                                    Container(
                                      height: 50,
                                      decoration: const BoxDecoration(
                                          color: CustomColors.orangeSoda40,
                                          borderRadius: BorderRadius.only(
                                              bottomLeft: Radius.circular(30),
                                              bottomRight:
                                                  Radius.circular(30))),
                                      child: Padding(
                                        padding: const EdgeInsets.symmetric(
                                            horizontal: 18.0),
                                        child: Row(
                                          mainAxisAlignment:
                                              MainAxisAlignment.spaceBetween,
                                          children: [
                                            const Text(
                                              'YOUR CURRENT RANKING',
                                              style: TextStyle(
                                                  letterSpacing: 3,
                                                  fontFamily: "Roboto",
                                                  fontWeight: FontWeight.bold,
                                                  color: Colors.white,
                                                  fontSize: 10),
                                            ),
                                            Row(
                                              children: [
                                                const Padding(
                                                  padding: EdgeInsets.only(
                                                      top: 6, right: 12.0),
                                                  child: Text(
                                                    '0',
                                                    style: TextStyle(
                                                        letterSpacing: 3,
                                                        fontFamily: "Roboto",
                                                        fontWeight:
                                                            FontWeight.bold,
                                                        color: Colors.white,
                                                        fontSize: 10),
                                                  ),
                                                ),
                                                Image.asset(
                                                  Assets.png.fire,
                                                  // 'assets/img/png/fire.png',
                                                  width: 20,
                                                ),
                                              ],
                                            )
                                          ],
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                            const Padding(
                              padding: EdgeInsets.only(top: 40.0),
                              child: Text(
                                'You must be at least 18 years old to access this app',
                                style: TextStyle(
                                    fontFamily: "Roboto",
                                    letterSpacing: 1.5,
                                    color: Colors.white,
                                    fontSize: 12),
                              ),
                            ),
                            Padding(
                              padding: const EdgeInsets.only(top: 13.0),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Radio(
                                      value: true,
                                      fillColor: WidgetStateColor.resolveWith(
                                          (states) => Colors.white),
                                      activeColor: Colors.white,
                                      onChanged: (bool? newValue) {
                                        _userStore.setIsGreaterThan18(
                                            newValue ?? false);
                                      },
                                      groupValue: store.state.isGreaterThan18),
                                  const Text(
                                    'Yes, I am over 18 years old.',
                                    style: TextStyle(
                                        fontFamily: "Roboto",
                                        fontWeight: FontWeight.w600,
                                        letterSpacing: 1.5,
                                        color: Colors.white,
                                        fontSize: 13),
                                  )
                                ],
                              ),
                            ),
                            Container(
                              margin: const EdgeInsets.only(top: 48),
                              width: double.infinity,
                              child: getButton(),
                            ),
                            Padding(
                              padding:
                                  const EdgeInsets.only(top: 108, bottom: 28.0),
                              child: IconButton(
                                  onPressed: handleOpenTutorial,
                                  icon: const Icon(QuyckyIcons.question_circle,
                                      color: Colors.white)),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ));
            }),
      ),
    );
  }
//
// JavascriptChannel _toasterJavascriptChannel(BuildContext context) {
//   return JavascriptChannel(
//       name: 'Toaster',
//       onMessageReceived: (JavascriptMessage message) {
//         ScaffoldMessenger.of(context).showSnackBar(
//           SnackBar(content: Text(message.message)),
//         );
//       });
// }
}
