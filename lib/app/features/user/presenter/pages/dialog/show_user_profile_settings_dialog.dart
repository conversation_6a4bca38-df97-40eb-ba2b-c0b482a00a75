import 'package:flutter/material.dart';
import 'package:play_to_vibe/app/features/user/presenter/pages/widgets/user_profile_settings_menu.dart';
import 'package:play_to_vibe/app/presenter/app_widget.dart';

class ShowUserProfileSettings {
  ShowUserProfileSettings({required Future<bool> Function() handleSave}) {
    BuildContext context = AppWidget.globalKey.currentState!.context;
    showGeneralDialog(
      context: context,
      barrierDismissible: true,
      transitionDuration: const Duration(milliseconds: 300),
      barrierLabel: MaterialLocalizations.of(context).dialogLabel,
      barrierColor: Colors.black.withOpacity(0.5),
      pageBuilder: (context, _, __) => getDialogWidget(handleSave: handleSave),
      transitionBuilder: getTransitionBuilder,
    );
  }

  Widget getDialogWidget({required Future<bool> Function() handleSave}) {
    Map<Symbol, dynamic> args = {#handleSave: handleSave};
    return Function.apply(UserProfileSettingsMenu.new, [], args);
  }

  Widget getTransitionBuilder(BuildContext context, Animation<double> animation,
      Animation<double> secondaryAnimation, Widget child) {
    Offset begin = const Offset(0, -1);
    begin = (const Offset(0, 1));

    return SlideTransition(
      position: CurvedAnimation(
        parent: animation,
        curve: Curves.easeOut,
      ).drive(Tween<Offset>(
        begin: begin,
        end: Offset.zero,
      )),
      child: child,
    );
  }
}
