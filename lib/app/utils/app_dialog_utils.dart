import 'package:flutter/material.dart';
import 'package:play_to_vibe/app/widgets/report_player_dialog.dart';
import 'package:play_to_vibe/app/widgets/block_player_dialog.dart';

// Função para mostrar o diálogo de confirmação de bloqueio de jogador
Future<void> showAppBlockPlayerDialog(BuildContext context) async {
  // É importante verificar se o widget ainda está montado se houver operações assíncronas
  // antes de chamar showDialog, embora aqui seja direto.
  if (!context.mounted) return;

  return showDialog<void>(
    context: context,
    barrierDismissible: false, // O usuário deve interagir com o diálogo
    builder: (BuildContext dialogContext) {
      return BlockPlayerDialog(
        onDone: () {
          // A lógica de fechar o diálogo já está em BlockPlayerDialog (Navigator.of(dialogContext).pop();)
          // Adicione aqui qualquer lógica adicional que precise ser executada após o "Done".
          // Por exemplo, atualizar a UI, chamar uma API, etc.
          print(
              "AppDialogUtils: <PERSON><PERSON>r bloqueado, diálogo de confirmação fechado.");
        },
      );
    },
  );
}

// Função para mostrar o diálogo de reporte de jogador, que pode levar ao diálogo de bloqueio
Future<void> showAppReportPlayerDialog(BuildContext context) async {
  if (!context.mounted) return;

  return showDialog<void>(
    context: context,
    barrierDismissible: false,
    builder: (BuildContext dialogContext) {
      return ReportPlayerDialog(
        onBlockPlayer: () {
          // Primeiro, o ReportPlayerDialog se fecha (internamente ou explicitamente aqui se necessário).
          // O ReportPlayerDialog já chama Navigator.of(dialogContext).pop() em seu onPrimaryAction.
          // Se não chamasse, faríamos: Navigator.of(dialogContext).pop();

          // Então, mostra o BlockPlayerDialog.
          // É crucial usar o 'context' original (da tela/widget que chamou showAppReportPlayerDialog)
          // para o novo showDialog, para garantir que ele seja ancorado corretamente na árvore de widgets.
          showAppBlockPlayerDialog(context); // Passa o context original da tela
          print(
              "AppDialogUtils: Opção Bloquear Jogador selecionada após reporte.");
          // Adicione aqui a lógica real para iniciar o bloqueio do jogador
        },
        onReturn: () {
          // A lógica de fechar o diálogo já está em ReportPlayerDialog.
          print("AppDialogUtils: Opção Retornar selecionada após reporte.");
        },
      );
    },
  );
}
