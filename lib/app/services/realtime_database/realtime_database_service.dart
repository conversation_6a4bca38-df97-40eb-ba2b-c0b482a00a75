import 'package:firebase_database/firebase_database.dart';
import 'package:play_to_vibe/app/services/realtime_database/entities/promo_entity.dart';

class RealtimeDatabaseService {
  final String path;
  final DatabaseReference _databaseReference;

  RealtimeDatabaseService({required this.path})
      : _databaseReference = FirebaseDatabase.instance.ref(path);

  Future<DataSnapshot> list() async {
    return (await _databaseReference.once()).snapshot;
  }

  Future<DataSnapshot> read(String id) async {
    return (await _databaseReference.child(id).once()).snapshot;
  }

  Future<void> create(PromoEntity dados) async {
    return await _databaseReference.push().set(dados);
  }

  Future<void> edit(String id, PromoEntity dados) async {
    return await _databaseReference.child(id).update(dados.toJson());
  }
}
