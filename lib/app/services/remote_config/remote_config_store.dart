import 'package:flutter_triple/flutter_triple.dart';
import 'package:play_to_vibe/app/features/home/<USER>/storage/remote_config_storage.dart';
import 'package:play_to_vibe/app/services/remote_config/remote_config_entity.dart';
// import 'package:play_to_vibe/core/utils/interval_utils.dart';

class RemoteConfigStore extends Store<RemoteConfigEntity> {
  final RemoteConfigStorage _remoteConfigStorage;

  RemoteConfigStore(this._remoteConfigStorage)
      : super(const RemoteConfigEntity()) {
    // setTimeout(
    //     callback: updatePromotioDataFromStorage,
    //     duration: const Duration(seconds: 1));
  }

  updatePromotioDataFromStorage() async {
    try {
      final remoteConfigFromStorageData =
          await _remoteConfigStorage.getRemoteConfigStoreData() ??
              const RemoteConfigEntity();
      update(remoteConfigFromStorageData, force: true);
    } catch (e) {}
  }

  setData(RemoteConfigEntity newData) {
    update(newData, force: true);

    // _remoteConfigStorage.setRemoteConfigStoreData(newData);
  }
}
