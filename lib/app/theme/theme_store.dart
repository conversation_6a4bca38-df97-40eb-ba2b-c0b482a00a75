import 'package:play_to_vibe/app/theme/colors.dart';
import 'package:flutter/material.dart';

var darkTheme = ThemeData.dark();
var lightTheme = ThemeData.light();

enum ThemeType { light, dark }

final initialTheme = ThemeData(
    // fontFamily: 'Poppins',
    outlinedButtonTheme: OutlinedButtonThemeData(
      style: TextButton.styleFrom(
        side: BorderSide(color: colors.values.first, width: 1),
      ),
    ),
    inputDecorationTheme: InputDecorationTheme(
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(5))),
    colorScheme:
        ColorScheme.fromSwatch(accentColor: getAppMaterialColor(EColors.accent))
            .copyWith(
                primary: getAppMaterialColor(EColors.primary),
                secondary: getAppMaterialColor(EColors.secondary),
                error: getAppMaterialColor(EColors.error),
                surface: getAppMaterialColor(EColors.surface),
                onSurface: getAppMaterialColor(EColors.onSurface)));

class ThemeStore extends ChangeNotifier {
  ThemeData currentTheme = initialTheme;
  ThemeType _themeType = ThemeType.dark;

  toggleTheme() {
    if (_themeType == ThemeType.dark) {
      currentTheme = lightTheme;
      _themeType = ThemeType.light;
    } else if (_themeType == ThemeType.light) {
      currentTheme = darkTheme;
      _themeType = ThemeType.dark;
    }
    return notifyListeners();
  }

  setTheme(ThemeData themeData) {
    currentTheme = themeData;
    return notifyListeners();
  }
}
