import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:lottie/lottie.dart';
import 'package:play_to_vibe/app/theme/colors.dart';
import 'package:play_to_vibe/core/utils/image_assets.dart';

TextStyle buttonDefaultTextStyle(Color color,
        {double fontSize = 15,
        double letterSpacing = 3,
        FontWeight fontWeight = FontWeight.bold,
        String fontFamily = 'Roboto',
        double height = 0}) =>
    TextStyle(
        letterSpacing: letterSpacing,
        fontFamily: fontFamily,
        fontWeight: fontWeight,
        height: height,
        color: color,
        fontSize: fontSize);

class Button extends StatefulWidget {
  final double fontSize;
  final bool upperCase;
  final EdgeInsetsGeometry padding;
  final bool autoSized;
  final void Function()? onPressed;
  final void Function()? executeBeforeAnimation;
  final String text;
  final Color color;
  final Color circleButtonColor;
  final Color textColor;
  final Color borderColor;
  final bool outlined;
  final Widget? child;
  final bool circleButton;
  final double circleButtonSize;
  final String assetImageUrl;
  final Color? shadowColor;
  final double? assetImageWidth;
  final double? assetImageHeight;
  final bool noAnimation;

  const Button({
    super.key,
    this.executeBeforeAnimation,
    @required this.onPressed,
    this.text = "",
    this.fontSize = 15,
    this.circleButton = false,
    this.circleButtonSize = 38,
    this.circleButtonColor = Colors.white,
    this.autoSized = false,
    this.padding = const EdgeInsets.symmetric(horizontal: 38),
    this.upperCase = true,
    this.color = CustomColors.button,
    this.textColor = Colors.white,
    this.outlined = false,
    this.borderColor = CustomColors.button,
    this.assetImageUrl = ImageAssets.fire,
    this.child,
    this.shadowColor = Colors.white,
    this.assetImageWidth = 21,
    this.assetImageHeight = 21,
    this.noAnimation = false,
  });

  @override
  State<Button> createState() => _Button();
}

class _Button extends State<Button> {
  bool isClicked = false;

  void onPressed() {
    if (widget.executeBeforeAnimation != null) {
      widget.executeBeforeAnimation!();
    }
    if (!widget.noAnimation) {
      setState(() {
        isClicked = true;
      });
      Future.delayed(
          const Duration(milliseconds: 2500),
          () => mounted
              ? setState(() {
                  isClicked = false;
                })
              : null);
      Future.delayed(
          const Duration(milliseconds: 500), () => widget.onPressed!());
      return;
    }
    widget.onPressed!();
  }

  String getText() {
    return widget.upperCase ? widget.text.toUpperCase() : widget.text;
  }

  Widget getChild() {
    return widget.child ??
        Text(
          getText(),
          style: buttonDefaultTextStyle(widget.textColor,
              fontSize: widget.fontSize),
        );
  }

  Widget getDefaultButton() {
    final constraints = widget.autoSized
        ? const BoxConstraints(minHeight: 51, minWidth: double.infinity)
        : const BoxConstraints(minHeight: 51);
    return Container(
      constraints: constraints,
      child: TextButton(
          onPressed: onPressed,
          style: ButtonStyle(
              /**  padding: MaterialStateProperty.all<EdgeInsets>(
                  const EdgeInsets.symmetric(horizontal: 14, vertical: 16)),*/
              backgroundColor: WidgetStateProperty.all<Color>(
                  widget.outlined ? Colors.transparent : widget.color),
              shape: WidgetStateProperty.all<RoundedRectangleBorder>(
                  RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(30.0),
                      side: BorderSide(color: widget.borderColor, width: 1)))),
          child: getChild()),
    );
  }

  Widget getCircleButton() {
    return ElevatedButton(
        style: ElevatedButton.styleFrom(
            fixedSize: Size(widget.circleButtonSize, widget.circleButtonSize),
            padding: const EdgeInsets.all(10),
            elevation: 10,
            shape: const CircleBorder(),
            backgroundColor: widget.circleButtonColor,
            shadowColor: widget.shadowColor),
        onPressed: onPressed,
        child: widget.child ??
            SizedBox(
                height: widget.assetImageHeight,
                width: widget.assetImageWidth,
                child: widget.assetImageUrl.contains('.svg')
                    ? SvgPicture.asset(
                        widget.assetImageUrl,
                        fit: BoxFit.fill,
                      )
                    : Image.asset(widget.assetImageUrl)));
  }

  Widget getAutoGrowButton() {
    return Padding(
      padding: widget.padding,
      child: ButtonTheme(minWidth: double.infinity, child: getDefaultButton()),
    );
  }

  Widget getAnimation() {
    return Lottie.asset("assets/animations/flashing.json", height: 60);
  }

  Widget getWidget() {
    // if (isClicked && !widget.noAnimation) {
    //   return getAnimation();
    // }
    return widget.circleButton
        ? getCircleButton()
        : (widget.autoSized ? getDefaultButton() : getAutoGrowButton());
  }

  @override
  Widget build(BuildContext context) {
    return getWidget();
  }
}
