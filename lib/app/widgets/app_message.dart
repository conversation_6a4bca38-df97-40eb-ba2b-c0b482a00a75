import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:play_to_vibe/app/theme/colors.dart';
import 'package:play_to_vibe/core/utils/assets_strings.dart';
import 'package:play_to_vibe/core/utils/interval_utils.dart';
import 'package:play_to_vibe/app/widgets/avatar.dart';
import 'package:play_to_vibe/app/widgets/button.dart';

enum AppMessageAlignment { top, center, bottom }

class AppMessage extends StatefulWidget {
  final String imagePath;
  final AppMessageAlignment alignment;
  final String message;
  final Widget? child;
  final bool noButton;
  final bool noAvatar;
  final void Function()? onPressed;
  final void Function()? callback;
  final Color backgroundcolor;
  final String buttonLabel;
  final Color textColor;
  final Duration? duration;
  final bool noIcon;
  final Color iconColor;
  final IconData icon;
  final double iconSize;
  final double fontSize;

  const AppMessage(
      {super.key,
      this.iconSize = 28,
      this.iconColor = Colors.white,
      this.noAvatar = false,
      this.icon = Icons.warning,
      this.noIcon = true,
      this.duration,
      this.alignment = AppMessageAlignment.top,
      this.onPressed,
      this.callback,
      this.noButton = false,
      this.message = 'Hi!',
      this.buttonLabel = 'Ok',
      this.textColor = CustomColors.americanPurple2,
      this.backgroundcolor = CustomColors.cultured90,
      this.fontSize = 13,
      this.child,
      this.imagePath = ''});

  @override
  State<AppMessage> createState() => _AppMessage();
}

class _AppMessage extends State<AppMessage> {
  bool isClicked = false;
  bool closeCalled = false;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    if (widget.duration != null) {
      setTimeout(duration: widget.duration, callback: () => close());
    }
  }

  void close({fromOnPressed = false}) {
    if (closeCalled) return;
    closeCalled = true;
    Navigator.pop(context);
    if (widget.callback != null) {
      setTimeout(
          duration: const Duration(seconds: 1),
          callback: () {
            widget.callback!();
          });
    }
  }
  // fromOnPressed ?
  // : onPressed();

  void onPressed() {
    if (widget.onPressed != null) {
      widget.onPressed!();
    }
    close(fromOnPressed: true);
  }

  Widget getTextWidget() =>
      widget.child ??
      Text(widget.message,
          textAlign: TextAlign.start,
          overflow: TextOverflow.ellipsis,
          maxLines: 20,
          style: TextStyle(
              overflow: TextOverflow.clip,
              color: widget.textColor,
              fontWeight: FontWeight.w400,
              fontSize: widget.fontSize,
              decoration: TextDecoration.none));

  // Widget getMessage2() => ListTile(
  //       trailing: getButton(),
  //       leading: widget.noIcon ? getAvatar() : getIcon(),
  //       title: getTextWidget(),
  //     );

  Widget getMessage() => Expanded(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 14),
          child: getTextWidget(),
        ),
      );

  MainAxisAlignment getAlignment() {
    switch (widget.alignment) {
      case AppMessageAlignment.top:
        return MainAxisAlignment.start;
      case AppMessageAlignment.center:
        return MainAxisAlignment.center;
      case AppMessageAlignment.bottom:
        return MainAxisAlignment.end;
    }
  }

  Widget getAvatar() => widget.imagePath.isNotEmpty
      ? Avatar(
          addPhotoButton: false,
          size: 53,
          imagePath: widget.imagePath,
        )
      : SvgPicture.asset(Assets.svg.quyckyLogotype);

  Widget getIcon() => Icon(
        widget.icon,
        color: widget.iconColor,
        size: widget.iconSize,
      );

  Widget getImage() {
    return Padding(
        padding: const EdgeInsets.only(left: 8, right: 8),
        child: widget.noIcon ? getAvatar() : getIcon());
  }

  Widget getAvatarAndMessage() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.start,
      mainAxisSize: MainAxisSize.max,
      children: <Widget>[getImage(), getMessage(), getButton()],
    );
  }

  Widget getButton() => widget.noButton
      ? Container()
      : Container(
          padding: const EdgeInsets.only(right: 8),
          height: 30,
          width: 113,
          child: Button(
            fontSize: 13,
            autoSized: true,
            text: widget.buttonLabel,
            onPressed: onPressed,
          ),
        );

  Widget getWidget() {
    return Container(
      constraints: const BoxConstraints(minHeight: 70, maxWidth: 500),
      margin: const EdgeInsets.symmetric(horizontal: 5, vertical: 20),
      decoration: BoxDecoration(
          borderRadius: const BorderRadius.all(Radius.circular(10)),
          color: widget.backgroundcolor),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 5),
        child: getAvatarAndMessage(),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: SafeArea(
        child: Column(
          mainAxisSize: MainAxisSize.max,
          mainAxisAlignment: getAlignment(),
          children: [
            getWidget(),
          ],
        ),
      ),
    );
  }
}
