import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:play_to_vibe/app/features/user/presenter/controllers/user_controller.dart';
import 'package:play_to_vibe/app/theme/colors.dart';
import 'package:play_to_vibe/app/widgets/button.dart';
import 'package:sizer/sizer.dart';

class DialogSuspendedAccountWarning extends StatefulWidget {
  const DialogSuspendedAccountWarning({super.key});

  @override
  State<DialogSuspendedAccountWarning> createState() =>
      _DialogSuspendedAccountWarning();
}

class _DialogSuspendedAccountWarning
    extends State<DialogSuspendedAccountWarning> {
  final _userController = Modular.get<UserController>();

  @override
  void initState() {
    super.initState();
  }

  void handleClose() async {
    Navigator.pop(context);
    await _userController.doLogout();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: Colors.black.withAlpha(185),
        body: SizedBox(
            height: 100.h,
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text('ACCOUNT SUSPENDED',
                      style: const TextStyle(
                          letterSpacing: 1,
                          fontFamily: "Roboto",
                          fontWeight: FontWeight.w800,
                          color: Colors.white,
                          fontSize: 25)),
                  SizedBox(height: 3.55.h),
                  Text(
                      'Your account has been suspended due to violations of our community guidelines. Contact our <NAME_EMAIL>',
                      textAlign: TextAlign.center,
                      style: const TextStyle(
                          letterSpacing: 0.25,
                          fontFamily: "Roboto",
                          fontWeight: FontWeight.w500,
                          color: Colors.white,
                          fontSize: 14)),
                  SizedBox(height: 3.55.h),
                  SizedBox(
                    width: 56.41.w,
                    height: 5.6.h,
                    child: Button(
                      autoSized: true,
                      text: 'I UNDERSTAND',
                      color: Colors.white,
                      borderColor: Colors.white,
                      textColor: CustomColors.primary,
                      onPressed: handleClose,
                    ),
                  ),
                ],
              ),
            )));
  }
}
