import 'package:flutter/material.dart';
import 'package:play_to_vibe/app/theme/colors.dart';
import 'package:play_to_vibe/core/utils/get_image_from.dart';
import 'package:play_to_vibe/app/widgets/button.dart';
import 'package:play_to_vibe/core/utils/quycky_icons_icons.dart';
import 'package:sizer/sizer.dart';

class DialogConfirmation extends StatefulWidget {
  final void Function()? onOk;
  final void Function()? onClose;
  final String title;
  final String text;
  final String buttonText;
  final String buttonCancelText;
  final bool showCloseButton;
  final Color titleColor;
  final Color textColor;
  Widget? titleWidget;
  Widget? textWidget;
  bool showCancelButton;

  DialogConfirmation(
      {super.key,
      this.title = '',
      this.onOk,
      this.onClose,
      this.titleColor = CustomColors.americanPurple2,
      this.textColor = CustomColors.americanPurple2,
      this.text = '',
      this.buttonText = 'Ok',
      this.buttonCancelText = 'Cancel',
      this.showCancelButton = false,
      this.showCloseButton = true,
      this.titleWidget,
      this.textWidget});

  @override
  State<DialogConfirmation> createState() => _DialogConfirmation();
}

class _DialogConfirmation extends State<DialogConfirmation> {
  final PickImage pickImage = PickImage();
  @override
  void initState() {
    // TODO: implement initState
    super.initState();
  }

  void handleOnOk() {
    if (widget.onOk != null) widget.onOk!();
    Navigator.pop(context);
  }

  void close() {
    if (widget.onClose != null) {
      widget.onClose!();
    }
    Navigator.pop(context);
  }

  Widget getWidget() {
    return Container(
      constraints: const BoxConstraints(minHeight: 70, maxWidth: 325),
      decoration: const BoxDecoration(
          borderRadius: BorderRadius.all(Radius.circular(20)),
          color: Colors.white),
      child: Column(
        children: [
          Padding(
            padding: const EdgeInsets.only(top: 2, right: 2.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                Material(
                  color: Colors.transparent,
                  child: widget.showCloseButton
                      ? IconButton(
                          iconSize: 29,
                          onPressed: close,
                          color: Colors.red,
                          icon: const Icon(QuyckyIcons.close_circle))
                      : const SizedBox(height: 38),
                )
              ],
            ),
          ),
          Padding(
            padding: const EdgeInsets.only(top: 0, right: 0.0),
            child: Center(
              child: widget.titleWidget ??
                  Text(
                    widget.title,
                    style: TextStyle(
                        fontFamily: 'Roboto',
                        fontSize: 16,
                        color: widget.titleColor,
                        letterSpacing: 1,
                        fontWeight: FontWeight.w800,
                        decoration: TextDecoration.none),
                  ),
            ),
          ),
          Container(
            padding: EdgeInsets.symmetric(vertical: 28, horizontal: 4.w),
            child: Center(
              child: widget.textWidget ??
                  Text(
                    widget.text,
                    textAlign: TextAlign.center,
                    style: TextStyle(
                        fontFamily: 'Roboto',
                        fontSize: 14,
                        color: widget.textColor,
                        letterSpacing: 1,
                        fontWeight: FontWeight.w400,
                        decoration: TextDecoration.none),
                  ),
            ),
          ),
          Container(
            constraints: const BoxConstraints(
              maxWidth: 230,
            ),
            width: 230,
            padding: const EdgeInsets.only(bottom: 30),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              mainAxisSize: MainAxisSize.max,
              children: [
                Button(
                    autoSized: true,
                    outlined: widget.showCancelButton,
                    onPressed: handleOnOk,
                    child: Center(
                      child: Text(
                        widget.buttonText,
                        textAlign: TextAlign.center,
                        style: TextStyle(
                            fontFamily: 'Roboto',
                            fontSize: 15,
                            color: widget.showCancelButton
                                ? Colors.redAccent
                                : Colors.white,
                            letterSpacing: 1.5,
                            fontWeight: FontWeight.bold,
                            decoration: TextDecoration.none),
                      ),
                    )),
                Container(
                  padding: const EdgeInsets.only(top: 14),
                  child: widget.showCancelButton
                      ? Button(
                          autoSized: true,
                          text: widget.buttonCancelText,
                          onPressed: close)
                      : null,
                )
              ],
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        getWidget(),
      ],
    );
  }
}
