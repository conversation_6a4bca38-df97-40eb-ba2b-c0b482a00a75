import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:play_to_vibe/app/theme/colors.dart';
import 'package:play_to_vibe/app/widgets/button.dart';
// import 'package:play_to_vibe/app/widgets/report_player_dialog.dart'; // Removido
// import 'package:play_to_vibe/app/widgets/block_player_dialog.dart'; // Removido
import 'package:sizer/sizer.dart';

class FriendOptionsBottomSheet extends StatelessWidget {
  final VoidCallback? doInviteToPlay;
  final VoidCallback? doEraseChatHistory;
  final VoidCallback? doUnmatchPlayer;
  final VoidCallback? doReportPlayer;
  final VoidCallback? doBlockPlayer;

  const FriendOptionsBottomSheet({
    super.key,
    this.doInviteToPlay,
    this.doEraseChatHistory,
    this.doUnmatchPlayer,
    this.doReportPlayer,
    this.doBlockPlayer,
  });

  // Removidos os métodos _showReportPlayerDialog e _showBlockPlayerDialog

  @override
  Widget build(BuildContext context) {
    return ClipRRect(
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(20.0),
          topRight: Radius.circular(20.0),
        ),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 70, sigmaY: 70),
          child: Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  Colors.white.withValues(alpha: 0.25),
                  Colors.white.withValues(alpha: 0.15),
                ],
              ),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(20.0),
                topRight: Radius.circular(20.0),
              ),
              border: Border.all(
                color: Colors.white.withValues(alpha: 0.2),
                width: 1.0,
              ),
            ),
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: 6.w, vertical: 2.h),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: <Widget>[
                  Container(
                    width: 12.w,
                    height: 0.6.h,
                    decoration: BoxDecoration(
                      color: Colors.grey[300],
                      borderRadius: BorderRadius.circular(10.0),
                    ),
                  ),
                  SizedBox(height: 3.h),
                  _buildOptionButton(
                    text: 'INVITE TO PLAY',
                    onPressed: () {
                      Navigator.pop(context);
                      doInviteToPlay?.call();
                    },
                    textColor: CustomColors.primary,
                    backgroundColor: Colors.white,
                  ),
                  SizedBox(height: 1.5.h),
                  _buildOptionButton(
                    text: 'ERASE CHAT HISTORY',
                    isOutlined: true,
                    onPressed: () {
                      Navigator.pop(context);
                      doEraseChatHistory?.call();
                    },
                  ),
                  SizedBox(height: 1.5.h),
                  _buildOptionButton(
                    text: 'UNMATCH PLAYER',
                    isOutlined: true,
                    onPressed: () {
                      Navigator.pop(context);
                      doUnmatchPlayer?.call();
                    },
                  ),
                  SizedBox(height: 1.5.h),
                  _buildOptionButton(
                    text: 'REPORT PLAYER',
                    isOutlined: true,
                    onPressed: () {
                      Navigator.pop(context);
                      doReportPlayer?.call();
                    },
                  ),
                  SizedBox(height: 1.5.h),
                  _buildOptionButton(
                    text: 'BLOCK PLAYER',
                    isOutlined: true,
                    onPressed: () {
                      Navigator.pop(context);
                      doBlockPlayer?.call();
                    },
                  ),
                  SizedBox(height: 7.58.h), // Espaço extra na parte inferior
                ],
              ),
            ),
          ),
        ));
  }

  Widget _buildOptionButton({
    required String text,
    required VoidCallback onPressed,
    Color backgroundColor = Colors.transparent,
    Color? textColor,
    bool isOutlined = false,
  }) {
    return SizedBox(
        width: 87.18.w,
        height: 5.8.h,
        child: Button(
          autoSized: true,
          text: text,
          onPressed: onPressed,
          borderColor: Colors.white,
          outlined: isOutlined,
          color: backgroundColor,
          textColor: textColor ?? Colors.white,
        ));
  }
}
