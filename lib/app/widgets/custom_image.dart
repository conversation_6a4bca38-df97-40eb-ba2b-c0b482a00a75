import 'dart:io';
import 'dart:typed_data';

import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:skeletonizer/skeletonizer.dart';
import 'package:play_to_vibe/core/utils/assets_strings.dart';

class CustomImage extends StatefulWidget {
  final double? width;
  final double? height;
  final String imagePath;
  final BoxFit fit;
  final void Function()? onPressed;
  late final String defaultImagePath;
  final BorderRadius? borderRadius;
  final Uint8List? imageMemory;
  final ColorFilter? colorFilter;

  CustomImage(this.imagePath,
      {super.key,
      this.onPressed,
      this.width,
      this.fit = BoxFit.fill,
      this.height,
      this.borderRadius,
      String? defaultImagePath,
      this.colorFilter,
      this.imageMemory}) {
    this.defaultImagePath = defaultImagePath ?? Assets.svg.backgrounds.rom;
  }

  @override
  State<CustomImage> createState() => _CustomImage();
}

class _CustomImage extends State<CustomImage> {
  bool isClicked = false;

  void onPressed() {
    widget.onPressed!();
  }

  // Widget getImageFromMemory({dynamic imageMemory}) => Image.memory(
  //       widget.imageMemory ?? imageMemory,
  //       width: widget.size,
  //       height: widget.size,
  //       fit: BoxFit.cover,
  //     );

  Widget getImageFromAssets(String imagePath) => imagePath.endsWith('svg')
      ? SvgPicture.asset(imagePath,
          fit: widget.fit, colorFilter: widget.colorFilter)
      : Image.asset(imagePath, fit: widget.fit);

  Widget getDefaultAvatarImageFromAsset() =>
      getImageFromAssets(widget.defaultImagePath);

  Widget getImageFromFile() => Image.file(
        File(widget.imagePath),
        fit: widget.fit,
      );

  Widget getSvgImageFromNetwork() => SvgPicture.network(
        widget.imagePath,
        width: widget.width,
        height: widget.height,
        fit: widget.fit,
      );

  Widget getImageFromNetwork() => Image.network(
        loadingBuilder: (BuildContext context, Widget child,
            ImageChunkEvent? loadingProgress) {
          if (loadingProgress == null) return child;
          return Skeletonizer(child: getDefaultAvatarImageFromAsset());
        },
        widget.imagePath,
        width: widget.width,
        height: widget.height,
        fit: widget.fit,
      );

  Widget getImageFromMemory({dynamic imageMemory}) => Image.memory(
        widget.imageMemory ?? imageMemory,
        width: widget.width,
        height: widget.height,
        fit: widget.fit,
      );

  Widget getCorrectImageWidget() {
    if (widget.imageMemory != null && widget.imageMemory!.isNotEmpty) {
      return getImageFromMemory();
    }
    if (widget.imagePath == '') {
      return getDefaultAvatarImageFromAsset();
    }
    if (widget.imagePath.startsWith('assets/')) {
      return getImageFromAssets(widget.imagePath);
    }
    if (widget.imagePath.startsWith('http')) {
      if (widget.imagePath.contains('svg')) {
        return getSvgImageFromNetwork();
      }
      return getImageFromNetwork();
    }

    return getImageFromFile();
  }

  Widget getFinalWidget() {
    final imageWidget = widget.onPressed != null
        ? GestureDetector(onTap: onPressed, child: getCorrectImageWidget())
        : getCorrectImageWidget();
    Widget res = widget.width != null && widget.height != null
        ? FittedBox(
            fit: BoxFit.none,
            child: SizedBox(
                width: widget.width, height: widget.height, child: imageWidget),
          )
        : imageWidget;

    if (widget.borderRadius != null) {
      return ClipRRect(
        borderRadius: widget.borderRadius!,
        child: res,
      );
    }
    return res;
  }

  @override
  Widget build(BuildContext context) {
    return getFinalWidget();
  }
}
