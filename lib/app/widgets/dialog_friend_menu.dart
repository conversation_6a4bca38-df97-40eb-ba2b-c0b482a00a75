import 'dart:typed_data';

import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:play_to_vibe/analytics.dart';
import 'package:play_to_vibe/app/features/friendship/domain/entities/report_player_params_entity.dart';
import 'package:play_to_vibe/app/features/friendship/presenter/controllers/friendship_controller.dart';
import 'package:play_to_vibe/app/features/user/domain/entities/user_entity.dart';
import 'package:play_to_vibe/app/presenter/app_widget.dart';
import 'package:play_to_vibe/app/theme/colors.dart';
import 'package:play_to_vibe/app/widgets/app_header.dart';
import 'package:play_to_vibe/app/widgets/avatar.dart';
import 'package:play_to_vibe/app/widgets/block_player_dialog.dart';
import 'package:play_to_vibe/app/widgets/button.dart';
import 'package:play_to_vibe/app/widgets/gradient_container.dart';
import 'package:play_to_vibe/app/widgets/quycky_modal_progress.dart';
import 'package:play_to_vibe/core/utils/quycky_icons_icons.dart';
import 'package:play_to_vibe/core/utils/show_message.dart';
import 'package:sizer/sizer.dart';

enum ECurrentFriendMenuState {
  initial,
  flagAbusiveContent,
  blockUser,
  endFlagAbusiveMenu,
  endMenu
}

class DialogFriendMenu extends StatefulWidget {
  final ECurrentFriendMenuState initialScreen;
  final UserEntity user;
  final Uint8List? playerImageMemoryData;
  final bool closeOnReportPlayer;
  final void Function()? onSuccessPlayerReport;

  const DialogFriendMenu(
      {super.key,
      required this.user,
      required this.playerImageMemoryData,
      this.initialScreen = ECurrentFriendMenuState.initial,
      this.onSuccessPlayerReport,
      this.closeOnReportPlayer = false});

  @override
  State<DialogFriendMenu> createState() => _DialogFriendMenu();
}

class _DialogFriendMenu extends State<DialogFriendMenu> {
  bool isLoading = false;
  String title = 'ADD FRIEND';
  String subtitle = '';
  ECurrentFriendMenuState currentMenuState = ECurrentFriendMenuState.initial;
  final _friendshipController = Modular.get<FriendshipController>();

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    currentMenuState = widget.initialScreen;
    Analytics.instance.logEvent(name: 'play_open_profile');
  }

  void handleClose() => Navigator.pop(context);

  void onPressed() {
    handleClose();
    //  widget.onPressed!();
  }

  Widget getButton(String label,
          {color = CustomColors.button,
          borderColor = CustomColors.button,
          textColor = Colors.white,
          bool outlined = false,
          void Function()? onPressed}) =>
      Container(
        margin: const EdgeInsets.only(bottom: 14),
        constraints: const BoxConstraints(maxWidth: 325),
        width: double.infinity,
        height: 51,
        child: Button(
          onPressed: onPressed,
          outlined: outlined,
          autoSized: true,
          textColor: textColor,
          color: color,
          text: label,
          borderColor: borderColor,
        ),
      );
  setLoading(bool loadingState) {
    setState(() {
      isLoading = loadingState;
    });
  }

  handleAddFriend() async {
    setLoading(true);
    _friendshipController.addFriend(widget.user.id);
    setLoading(false);
    setState(() {
      subtitle = 'FRIEND REQUEST SENT';
      currentMenuState = ECurrentFriendMenuState.endMenu;
    });
  }

  handleBlockUser() {
    setState(() {
      title = 'USER BLOCKED';
      subtitle = '';
      currentMenuState = ECurrentFriendMenuState.endMenu;
    });
  }

  handleShowEndFlagAbusiveContent() {
    setState(() {
      title = 'FRIEND PROFILE';
      currentMenuState = ECurrentFriendMenuState.endFlagAbusiveMenu;
    });
  }

  Widget getInitialMenuButtons() {
    List<Widget> children = [];
    if (!_friendshipController.isPlayerAlreadyFriend(widget.user)) {
      children.add(getButton('Add Friend',
          onPressed: handleAddFriend,
          textColor: CustomColors.primary,
          color: Colors.white));
    }
    children.add(getButton('REPORT PLAYER',
        onPressed: _doShowReportPlayerMenu,
        outlined: true,
        borderColor: Colors.white,
        color: Colors.white));
    children.add(getButton('Block User',
        onPressed: handleBlockUser,
        outlined: true,
        borderColor: Colors.white,
        color: Colors.white));

    return Column(
      children: children,
    );
  }

  Widget getReportPlayerMenu() {
    List<Widget> children = [];
    if (!_friendshipController.isPlayerAlreadyFriend(widget.user)) {
      children.add(getButton('Add Friend',
          onPressed: handleAddFriend,
          textColor: CustomColors.primary,
          color: Colors.white));
    }
    children.add(getButton('REPORT PLAYER',
        onPressed: _doShowReportPlayerMenu,
        outlined: true,
        borderColor: Colors.white,
        color: Colors.white));
    children.add(getButton('Block User',
        onPressed: handleBlockUser,
        outlined: true,
        borderColor: Colors.white,
        color: Colors.white));

    return Column(
      children: children,
    );
  }

  handleGoToFlagAbusiveContentMenu() {
    setState(() {
      title = 'REPORT PLAYER';
      subtitle = 'WHY DO YOU WANT TO REPORT?';
      currentMenuState = ECurrentFriendMenuState.flagAbusiveContent;
    });
  }

  void _doBlockPlayer() async {
    BuildContext pageContext = AppWidget.globalKey.currentState!.context;
    final controller = Modular.get<FriendshipController>();
    final res = await controller.blockUser(int.parse(widget.user.id));
    if (res) {
      showDialog(
        context: pageContext, // Usa o context da página para o showDialog
        barrierDismissible: false,
        builder: (BuildContext dialogContext) {
          return BlockPlayerDialog(
            onDone: () {},
          );
        },
      );
      return;
    }
    ShowMessage(
      noAvatar: true,
      message:
          'There was a problem blocking the player, please try again later',
    );
  }

  void _doShowReportPlayerMenu() {
    setState(() {
      currentMenuState = ECurrentFriendMenuState.flagAbusiveContent;
      title = 'REPORT PLAYER';
    });
  }

  void _doReportPlayer(ReportReason reason) async {
    // BuildContext pageContext = AppWidget.globalKey.currentState!.context;
    final controller = Modular.get<FriendshipController>();
    final res = await controller.reportUser(int.parse(widget.user.id), reason);
    if (res == 1) {
      if (widget.closeOnReportPlayer) {
        handleClose();
      }
      if (widget.onSuccessPlayerReport != null) {
        widget.onSuccessPlayerReport!();
      }
      handleShowEndFlagAbusiveContent();
      return;
    }
    if (res == 0) {
      ShowMessage(
        noAvatar: true,
        message: 'This player has already been reported by you.',
        noButton: true,
        duration: Duration(seconds: 3),
      );
      return;
    }
    ShowMessage(
      noAvatar: true,
      message:
          'There was a problem reporting the player, please try again later',
      noButton: true,
      duration: Duration(seconds: 3),
    );
  }

  handleReportAbusiveContent() {
    setState(() {
      title = 'ABUSIVE CONTENT REPORTED';
      subtitle = '';
      currentMenuState = ECurrentFriendMenuState.endMenu;
    });
  }

  Widget getEndFlagAbusiveContentMenuButtons() {
    return Column(
      children: [
        Text(
          'PLAYER HAS BEEN REPORTED',
          style: TextStyle(
              letterSpacing: 4,
              fontFamily: "Roboto",
              fontWeight: FontWeight.w700,
              color: Colors.white,
              fontSize: 12),
        ),
        SizedBox(
          height: 3.55.h,
        ),
        getButton('Return',
            onPressed: handleClose,
            textColor: CustomColors.primary,
            color: Colors.white),
      ],
    );
  }

  Widget getEndMenuButtons() {
    return getButton('Back to Game',
        onPressed: handleClose,
        textColor: CustomColors.primary,
        color: Colors.white);
  }

  Widget getFlagAbusiveContentMenuButtons() {
    return Column(
      children: [
        Text(
          'REPORT MOTIVE',
          style: TextStyle(
              letterSpacing: 4,
              fontFamily: "Roboto",
              fontWeight: FontWeight.w700,
              color: Colors.white,
              fontSize: 12),
        ),
        SizedBox(
          height: 3.55.h,
        ),
        getButton('TOXIC VIBES',
            onPressed: () => _doReportPlayer(ReportReason.toxicVibes),
            borderColor: Colors.white,
            color: Colors.white,
            textColor: CustomColors.primary),
        getButton('Playing Dirty',
            onPressed: () => _doReportPlayer(ReportReason.playingDirty),
            borderColor: Colors.white,
            color: Colors.white,
            textColor: CustomColors.primary),
        getButton('MISLEADING PROFILE',
            onPressed: () => _doReportPlayer(ReportReason.misleadingProfile),
            borderColor: Colors.white,
            color: Colors.white,
            textColor: CustomColors.primary),
      ],
    );
  }

  Widget getButtons() {
    switch (currentMenuState) {
      case ECurrentFriendMenuState.initial:
        return getInitialMenuButtons();
      case ECurrentFriendMenuState.flagAbusiveContent:
        return getFlagAbusiveContentMenuButtons();
      case ECurrentFriendMenuState.endFlagAbusiveMenu:
        return getEndFlagAbusiveContentMenuButtons();
      case ECurrentFriendMenuState.endMenu:
        return getEndMenuButtons();
    }
    return getInitialMenuButtons();
  }

  Widget getUserAvatarAndName() {
    return Column(
      children: [
        SizedBox(
          height: 11.h,
          width: 11.h,
          child: Stack(
            alignment: Alignment.center,
            children: [
              Avatar(
                size: 100.h,
                addPhotoButton: false,
                imageMemory: widget.playerImageMemoryData,
                borderColor: CustomColors.primary,
                // imagePath: widget.user.avatarUrl,
              ),
            ],
          ),
        ),
        Padding(
          padding: EdgeInsets.only(top: 0.4.h),
          child: Text(
            widget.user.name,
            style: const TextStyle(
                letterSpacing: 0.1,
                fontFamily: "Roboto",
                fontWeight: FontWeight.w700,
                color: Colors.white,
                fontSize: 18),
          ),
        )
      ],
    );
  }

  Widget getWidget() {
    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.only(top: 41.0),
          child: getUserAvatarAndName(),
        ),
        Padding(
          padding: const EdgeInsets.only(top: 40.0),
          child: getButtons(),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        body: GradientContainer(
            useDefault: true,
            coldOpacity: 0,
            normalOpacity: 0,
            hotOpacity: 0,
            child: SafeArea(
                child: QuyckyModalProgress(
              state: isLoading,
              child: Column(
                children: [
                  AppHeader(
                      title: title,
                      subtitle: subtitle,
                      logoSectionLeftWidget: IconButton(
                        icon: const Icon(
                          QuyckyIcons.arrow_left_circle,
                          color: Colors.white,
                          size: 23,
                        ),
                        onPressed: handleClose,
                      )),
                  getWidget(),
                  // const Spacer(),
                  // const Row(
                  //   mainAxisAlignment: MainAxisAlignment.center,
                  //   children: [
                  //     IconButton(
                  //         onPressed: null,
                  //         icon: Icon(
                  //           QuyckyIcons.question_circle,
                  //           color: Colors.white,
                  //         ))
                  //   ],
                  // ),
                  // const SizedBox(height: 30),
                ],
              ),
            ))));
  }
}
