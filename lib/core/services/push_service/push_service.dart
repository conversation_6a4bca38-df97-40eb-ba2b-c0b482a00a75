import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:play_to_vibe/analytics.dart';
import 'package:play_to_vibe/app/features/friendship/domain/usecases/accept_invite_usecase.dart';
import 'package:play_to_vibe/app/features/game/presenter/storage/game_storage.dart';
import 'package:play_to_vibe/app/features/user/presenter/controllers/user_controller.dart';
import 'package:play_to_vibe/app/features/user/presenter/storage/user_storage.dart';
import 'package:play_to_vibe/core/services/notification_service/local_notifications_service.dart';
import 'package:play_to_vibe/core/services/push_service/model/app_notification.dart';
import 'package:play_to_vibe/core/services/push_service/model/notification_entity.dart';
import 'package:play_to_vibe/core/utils/app_routes.dart';
import 'package:play_to_vibe/core/utils/interval_utils.dart';
import 'package:play_to_vibe/core/utils/show_message.dart';
import 'package:play_to_vibe/core/utils/use_cases/open_to_invited_game_room_usecase.dart';
import 'package:url_launcher/url_launcher.dart';

class PushService {
  final GameStorage gameStorage;
  final UserStorage userStorage;
  final AcceptInviteUseCase acceptInviteUseCase;
  final OpenToInvitedGameRoomUseCase openToInvitedGameRoomUseCaseUseCase;
  final UserController _userController;

  static final notificationState = NotificationState();

  static String _pushServiceKey = '';

  String get pushServiceKey => PushService._pushServiceKey;

  set pushServiceKey(String key) {
    if (key != PushService._pushServiceKey) {
      debugPrint('🔔 Push token changed, updating server...');
      PushService._pushServiceKey = key;
      userStorage.setPushId(key);
      updateUser();
    }
  }

  updateUser() async {
    try {
      final userData = _userController.getCurrentUser();
      final success = await _userController.updateUser(userData.name,
          appleId: userData.appleId, googleId: userData.googleId);
      if (success) {
        debugPrint('🔔 Push token updated on server successfully');
      } else {
        debugPrint('🔔 Failed to update push token on server');
      }
    } catch (e) {
      debugPrint('🔔 Error updating push token on server: $e');
    }
  }

  Future<String> getPushServiceKeyFromStorage() async {
    String res = '';
    try {
      res = (await userStorage.getPushId()) ?? '';
    } catch (e) {}
    return res;
  }

  PushService(
    this.gameStorage,
    this.userStorage,
    this.acceptInviteUseCase,
    this.openToInvitedGameRoomUseCaseUseCase,
    this._userController,
  );

  acceptInvite(String id) async {
    final result = await acceptInviteUseCase(int.parse(id));
    if (result.isLeft()) {
      ShowMessage(
          fontSize: 16,
          message: "Invitation accepted",
          type: MessageType.info,
          duration: const Duration(seconds: 3));
    }
  }

  onTapNotificationBackground(
      String type, String message, Map<String, dynamic> data) {
    try {
      switch (type) {
        case "INVITE_GAME_ROOM_PUSH_MESSAGE":
          {
            if (!Modular.to.path.contains(AppRoutes.game)) {
              openToInvitedGameRoomUseCaseUseCase(OpenToInvitedGameRoomData(
                  roomName: data['room_name'],
                  fromForegroundNotification: false));
            }
            break;
          }
        case "WHEN_USER_IDLE_PENDING_INVITATIONS_PUSH_MESSAGE":
          {
            break;
          }
        case "RE_ENGAGEMENT_24":
          {
            Analytics.instance.logEvent(name: 'reengagement_24');
            break;
          }
      }
    } catch (e) {
      print('err==>$e');
    }
  }

  String getPageName(String pageName) {
    switch (pageName) {
      case 'initial':
        return AppRoutes.initialRoute;
      case 'lobby':
        return AppRoutes.gameLobby();
      case 'home':
        return AppRoutes.home;
      case 'profile':
        return AppRoutes.userProfile(complete: true);
      case 'friends_list':
        return AppRoutes.userFriendsList;
      case 'vibe_check':
        return AppRoutes.checkFriendMatch;
    }
    return '';
  }

  void doAppNotificationAction(AppNotification data) {
    if (data.content.goToPage != '') {
      setTimeout(
          duration: const Duration(seconds: 1),
          callback: () {
            final pageToGo = getPageName(data.content.goToPage);
            if (Modular.to.path != pageToGo) {
              if (data.content.extraData.isNotEmpty) {
                Modular.to
                    .pushNamed(pageToGo, arguments: data.content.extraData);
                return;
              }
              Modular.to.pushNamed(pageToGo);
            }
          });
      return;
    }
    if (data.content.openUrl != '') {
      launchUrl(Uri.parse(data.content.openUrl),
          mode: LaunchMode.externalApplication);
    }
  }

  onReceiveNotificationForeground(
      String type, String? message, Map<String, dynamic> data,
      {openingApp = false}) {
    try {
      if (Modular.to.path.contains(AppRoutes.gamePlay())) {
        return;
      }
      switch (type) {
        case "INVITE_PUSH_MESSAGE":
          {
            ShowMessage(
                fontSize: 16,
                child: getFormattedMessage(
                    data['friendlyInvit']?['user']['name'],
                    ' sent you a friend request.'),
                // imagePath: data['friendlyInvit']['user']['avatarUrl'] ?? '',
                buttonLabel: 'ACCEPT',
                onPressed: () => acceptInvite(data['friendlyInvit']['id']),
                duration: const Duration(seconds: 3));
            break;
          }
        case "INVITE_ACCEPTED_PUSH_MESSAGE":
          {
            ShowMessage(
                fontSize: 16,
                child: getFormattedMessage(data['invited_user']?['name'],
                    ' accepted your friend request.'),
                onPressed: () {},
                // imagePath: data['invited_user']?['avatarUrl'] ?? '',
                duration: const Duration(seconds: 3));
            break;
          }
        case "APP_NOTIFICATION":
          {
            final appNotification = AppNotification.fromMap(data);
            if (openingApp) {
              doAppNotificationAction(appNotification);
              return;
            }
            void Function()? handlePressOnButton;
            String buttonLabel = '';
            if (appNotification.content.goToPage.isNotEmpty ||
                appNotification.content.openUrl.isNotEmpty) {
              buttonLabel = 'Go';
              handlePressOnButton =
                  () => doAppNotificationAction(appNotification);
            }
            ShowMessage(
                fontSize: 16,
                child: Text(
                  appNotification.message,
                  style: const TextStyle(
                      overflow: TextOverflow.clip,
                      color: Colors.black,
                      fontWeight: FontWeight.w400,
                      fontSize: 13,
                      decoration: TextDecoration.none),
                ),
                buttonLabel: buttonLabel,
                noButton: buttonLabel.isEmpty,
                onPressed: handlePressOnButton,
                // imagePath: data['invited_user']?['avatarUrl'] ?? '',
                duration: const Duration(seconds: 5));

            break;
          }
        case "WHEN_USER_IN_PUSH_MESSAGE":
          {
            ShowMessage(
                duration: const Duration(seconds: 3),
                child: getFormattedMessage(data['name'], ' is playing Quycky!'),
                noButton: true);
            break;
          }
        case "INVITE_GAME_ROOM_PUSH_MESSAGE":
          {
            if (openingApp) {
              setTimeout(
                  duration: const Duration(seconds: 1),
                  callback: () {
                    openToInvitedGameRoomUseCaseUseCase(
                        OpenToInvitedGameRoomData(roomName: data['room_name']));
                  });
              return;
            }
            if (!Modular.to.path.contains(AppRoutes.game)) {
              ShowMessage(
                child: getFormattedMessage(
                    data['user']?['name'], ' invited you to play'),
                duration: const Duration(seconds: 5),
                onPressed: () => openToInvitedGameRoomUseCaseUseCase(
                    OpenToInvitedGameRoomData(roomName: data['room_name'])),
                // imagePath: data['user']['avatarUrl'] ?? '',
              );
            }
            break;
          }
        case "FRIEND_VIBE_CHECK_PLAYER":
          {
            final appNotification = AppNotification.fromMap(data);
            if (!openingApp) {
              doAppNotificationAction(appNotification);
              return;
            }
            final friendId = data.containsKey('friend_id')
                ? data['friend_id'].toString()
                : data['content']['friend_id'].toString();
            if (Modular.to.path != AppRoutes.checkFriendMatch) {
              Modular.to
                  .pushNamed(AppRoutes.checkFriendMatch, arguments: friendId);
            }
            break;
          }
        case "WHEN_USER_IDLE_PENDING_INVITATIONS_PUSH_MESSAGE":
          {
            break;
          }
        case "RE_ENGAGEMENT_24":
          {
            Analytics.instance.logEvent(name: 'reengagement_24');
            break;
          }
      }
    } catch (e) {
      print('err==A>$e');
    }
  }

  Widget getFormattedMessage(String userName, String message) => RichText(
          text: TextSpan(children: [
        TextSpan(
            text: userName,
            style: const TextStyle(
                fontWeight: FontWeight.bold,
                color: Colors.black,
                fontSize: 13,
                decoration: TextDecoration.none)),
        TextSpan(
            text: message,
            style: const TextStyle(
                overflow: TextOverflow.clip,
                color: Colors.black,
                fontWeight: FontWeight.w400,
                fontSize: 13,
                decoration: TextDecoration.none)),
      ]));

  static onBackgroundMessage(int notificationHashCode, String type,
      String message, Map<String, dynamic> data) {
    try {
      String? payload;
      String? title;
      String? bodyMessage;
      switch (type) {
        case "INVITE_PUSH_MESSAGE":
          {
            title = data['friendlyInvit']?['user']['name'];
            bodyMessage =
                '${data['friendlyInvit']?['user']['name']} sent you a friend request.';
            Map<String, dynamic> informedData = {
              'type': type,
              'title': title,
              'message': bodyMessage,
              'data': data
            };
            payload = json.encode(informedData);
            break;
          }
        case "INVITE_ACCEPTED_PUSH_MESSAGE":
          {
            title = data['invited_user']?['name'];
            bodyMessage =
                '${data['invited_user']?['name']} accepted your friend request.';
            Map<String, dynamic> informedData = {
              'type': type,
              'title': title,
              'message':
                  '${data['invited_user']?['name']} accepted your friend request.',
              'data': data
            };
            payload = json.encode(informedData);
            break;
          }
        case "APP_NOTIFICATION":
          {
            title = data['title'];
            bodyMessage = data['message'];
            Map<String, dynamic> informedData = {
              'type': type,
              'title': title,
              'message': bodyMessage,
              'data': data
            };
            payload = json.encode(informedData);
            break;
          }
        case "WHEN_USER_IN_PUSH_MESSAGE":
          {
            title = data['user']?['name'];
            bodyMessage = '${data['user']?['name']} is playing Quycky!';
            Map<String, dynamic> informedData = {
              'type': type,
              'title': title,
              'message': bodyMessage,
              'data': data
            };
            payload = json.encode(informedData);
            break;
          }
        case "INVITE_GAME_ROOM_PUSH_MESSAGE":
          {
            title = data['user']?['name'];
            bodyMessage = '${data['user']?['name']} invited you to play';
            Map<String, dynamic> informedData = {
              'type': type,
              'title': title,
              'message': bodyMessage,
              'data': data
            };
            payload = json.encode(informedData);
            break;
          }
        case "WHEN_USER_IDLE_PENDING_INVITATIONS_PUSH_MESSAGE":
          {
            break;
          }
        case "RE_ENGAGEMENT_24":
          {
            Analytics.instance.logEvent(name: 'reengagement_24');
            break;
          }
      }
      // if (title != null && bodyMessage != null) {
      //   LocalNotificationService.showNotification(
      //       notificationHashCode, title, bodyMessage, payload);
      // }
    } catch (e) {
      print('err==A>$e');
    }
  }

  void showRegisteredNotification() {
    if (PushService.notificationState.currentNotification != null) {
      onReceiveNotificationForeground(
          PushService.notificationState.currentNotification!.type,
          PushService.notificationState.currentNotification!.message,
          PushService.notificationState.currentNotification!.data,
          openingApp: true);
      PushService.notificationState.setNotification(null);
    }
  }

  void startNotificationListeners() {
    PushService.notificationState.addListener(showRegisteredNotification);
  }
}

class NotificationState extends ChangeNotifier {
  NotificationEntity? currentNotification;

  void setNotification(NotificationEntity? notification) {
    currentNotification = notification;
    if (currentNotification != null) {
      notifyListeners();
    }
  }
}
