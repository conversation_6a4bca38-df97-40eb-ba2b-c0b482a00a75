import 'package:play_to_vibe/core/services/storage/storage_client.dart';
import 'package:shared_preferences/shared_preferences.dart';

const container = 'quycky_data';

class StorageSharedPreferencesClientImplementation implements StorageClient {
  SharedPreferences? _instance;

  // Singleton pattern para garantir uma única instância
  static StorageSharedPreferencesClientImplementation? _singleton;

  StorageSharedPreferencesClientImplementation._internal();

  factory StorageSharedPreferencesClientImplementation() {
    _singleton ??= StorageSharedPreferencesClientImplementation._internal();
    return _singleton!;
  }

  // Método para garantir que o SharedPreferences está inicializado
  Future<SharedPreferences> _getInstance() async {
    _instance ??= await SharedPreferences.getInstance();
    return _instance!;
  }

  @override
  Future<String> read(String key, {String? container}) async {
    try {
      final instance = await _getInstance();
      return instance.getString(key) ?? '';
    } catch (e) {
      // Em caso de erro, tenta criar uma nova instância
      final instance = await SharedPreferences.getInstance();
      return instance.getString(key) ?? '';
    }
  }

  @override
  Future<bool> write(String key, String data) async {
    try {
      final instance = await _getInstance();
      return await instance.setString(key, data);
    } catch (e) {
      // Em caso de erro, tenta criar uma nova instância
      final instance = await SharedPreferences.getInstance();
      return await instance.setString(key, data);
    }
  }

  @override
  Future<bool> erase() async {
    try {
      final instance = await _getInstance();
      return await instance.clear();
    } catch (e) {
      // Em caso de erro, tenta criar uma nova instância
      final instance = await SharedPreferences.getInstance();
      return await instance.clear();
    }
  }
}
