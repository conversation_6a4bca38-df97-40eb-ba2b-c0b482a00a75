import 'package:get_storage/get_storage.dart';
import 'package:play_to_vibe/core/services/storage/storage_client.dart';
import 'package:play_to_vibe/core/utils/app_storage_keys.dart';

class StorageGetStorageClientImplementation implements StorageClient {
  @override
  Future<String> read(String key) async {
    return (GetStorage(AppStorageKeys.defaultContainer).read<String>(key)) ??
        '';
  }

  @override
  Future<bool> write(String key, dynamic data, {String? container}) async {
    bool res = true;
    await GetStorage(container ?? AppStorageKeys.defaultContainer)
        .write(key, data)
        .catchError(() => res = false);
    return res;
  }

  @override
  Future<bool> erase() async {
    bool res = true;
    await GetStorage(AppStorageKeys.defaultContainer)
        .erase()
        .catchError(() => res = false);
    return res;
  }
}
