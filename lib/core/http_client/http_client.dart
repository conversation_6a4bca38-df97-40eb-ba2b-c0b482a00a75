import 'package:play_to_vibe/core/http_client/http_response.dart';

abstract class HttpClient {
  Future<HttpResponse> get(
    String url, {
    bool authenticated = true,
    Map<String, dynamic>? headers,
    Map<String, dynamic>? queryParameters,
    Duration? sendTimeout,
    Duration? receiveTimeout,
  });

  Future<HttpResponse> post(String url,
      {bool authenticated = true,
      Map<String, dynamic>? headers,
      Map<String, dynamic>? queryParameters,
      Duration? sendTimeout,
      Duration? receiveTimeout,
      dynamic body});

  Future<HttpResponse> put(String url,
      {bool authenticated = true,
      Map<String, dynamic>? headers,
      Map<String, dynamic>? queryParameters,
      Duration? sendTimeout,
      Duration? receiveTimeout,
      dynamic body});

  Future<HttpResponse> delete(String url,
      {bool authenticated = true,
      Map<String, dynamic>? headers,
      Map<String, dynamic>? queryParameters,
      Duration? sendTimeout,
      Duration? receiveTimeout,
      dynamic body});

  Future<HttpResponse> patch(String url,
      {bool authenticated = true,
      Map<String, dynamic>? headers,
      Map<String, dynamic>? queryParameters,
      Duration? sendTimeout,
      Duration? receiveTimeout,
      dynamic body});
}
