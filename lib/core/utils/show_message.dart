import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:play_to_vibe/app/presenter/app_widget.dart';
import 'package:play_to_vibe/app/theme/colors.dart';
import 'package:play_to_vibe/app/widgets/avatar.dart';
import 'package:play_to_vibe/app/widgets/app_message.dart';

enum MessageType { warning, error, info }

class ShowMessage {
  final AppMessageAlignment alignment;
  ShowMessage(
      {Duration? duration,
      noAvatar = false,
      double iconSize = 34,
      double fontSize = 13,
      MessageType type = MessageType.info,
      Color iconColor = Colors.white,
      IconData icon = Icons.warning,
      bool noIcon = true,
      Color textColor = CustomColors.americanPurple2,
      void Function()? onPressed,
      void Function()? callback,
      this.alignment = AppMessageAlignment.top,
      noButton = false,
      message = 'Hi!',
      buttonLabel = 'Ok',
      Color backgroundcolor = CustomColors.cultured90,
      Widget? child,
      imagePath = ''}) {
    BuildContext context = Modular.routerDelegate.navigatorKey
        .currentContext!; //AppWidget.globalKey.currentState!.context;
    showGeneralDialog(
      context: context,
      barrierDismissible: false,
      transitionDuration: const Duration(milliseconds: 500),
      barrierLabel: MaterialLocalizations.of(context).dialogLabel,
      barrierColor: Colors.black.withOpacity(0.5),
      pageBuilder: (context, _, __) => getAppMessage(
          fontSize: fontSize,
          noIcon: noIcon,
          iconSize: iconSize,
          iconColor: iconColor,
          icon: icon,
          noAvatar: noAvatar,
          duration: duration,
          onPressed: onPressed,
          callback: callback,
          noButton: noButton,
          textColor: textColor,
          message: message,
          buttonLabel: buttonLabel,
          alignment: alignment,
          backgroundcolor: backgroundcolor,
          type: type,
          child: child,
          imagePath: imagePath),
      transitionBuilder: getTransitionBuilder,
    );
  }

  Widget getAppMessage(
      {Duration? duration,
      double fontSize = 18,
      noAvatar = false,
      double iconSize = 28,
      Color iconColor = Colors.white,
      IconData icon = Icons.warning,
      bool noIcon = true,
      MessageType type = MessageType.info,
      Color textColor = Colors.white,
      void Function()? onPressed,
      void Function()? callback,
      AppMessageAlignment alignment = AppMessageAlignment.top,
      noButton = false,
      message = 'Hi!',
      buttonLabel = 'Ok',
      Color backgroundcolor = CustomColors.darkPurple,
      Widget? child,
      imagePath = defaultImagePath}) {
    Map<Symbol, dynamic> args = {
      #noIcon: noIcon,
      #iconSize: iconSize,
      #iconColor: iconColor,
      #icon: icon,
      #noAvatar: noAvatar,
      #duration: duration,
      #onPressed: onPressed,
      #callback: callback,
      #noButton: noButton,
      #textColor: textColor,
      #message: message,
      #buttonLabel: buttonLabel,
      #alignment: alignment,
      #backgroundcolor: backgroundcolor,
      #fontSize: fontSize,
      #imagePath: imagePath,
      #child: child,
    };
    switch (type) {
      case MessageType.error:
        {
          args.update(#backgroundcolor, (value) => Colors.deepOrange);
          args.update(#noIcon, (value) => false);
          args.update(#noAvatar, (value) => true);
          args.update(#icon, (value) => Icons.error);
          args.update(#iconColor, (value) => Colors.yellow);
          args.update(#duration, (value) => const Duration(seconds: 3));
          break;
        }
      case MessageType.warning:
        {
          args.update(#backgroundcolor, (value) => Colors.deepOrange);
          args.update(#noIcon, (value) => false);
          args.update(#noAvatar, (value) => true);
          args.update(#icon, (value) => Icons.warning);
          args.update(#iconColor, (value) => Colors.yellowAccent);
          break;
        }
      case MessageType.info:
        break;
    }
    return Function.apply(AppMessage.new, [], args);
  }

  Widget getTransitionBuilder(BuildContext context, Animation<double> animation,
      Animation<double> secondaryAnimation, Widget child) {
    Offset begin = const Offset(0, -1);
    if (alignment == AppMessageAlignment.bottom) {
      begin = (const Offset(0, 1));
    } else if (alignment == AppMessageAlignment.center) {
      begin = const Offset(-1, 0);
    }
    return SlideTransition(
      position: CurvedAnimation(
        parent: animation,
        curve: Curves.easeOut,
      ).drive(Tween<Offset>(
        begin: begin,
        end: Offset.zero,
      )),
      child: child,
    );
  }
}
