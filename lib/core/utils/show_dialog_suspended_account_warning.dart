import 'package:flutter/material.dart';
import 'package:play_to_vibe/app/presenter/app_widget.dart';
import 'package:play_to_vibe/app/widgets/dialog_suspended_account_warning.dart';

class ShowDialogSuspendedAccountWarning {
  ShowDialogSuspendedAccountWarning() {
    BuildContext context = AppWidget.globalKey.currentState!.context;
    showGeneralDialog(
      context: context,
      barrierDismissible: true,
      transitionDuration: const Duration(milliseconds: 500),
      barrierLabel: MaterialLocalizations.of(context).dialogLabel,
      barrierColor: Colors.black.withOpacity(0.5),
      pageBuilder: (context, _, __) => getAppMessage(),
      transitionBuilder: getTransitionBuilder,
    );
  }

  Widget getAppMessage() {
    return Function.apply(DialogSuspendedAccountWarning.new, []);
  }

  Widget getTransitionBuilder(BuildContext context, Animation<double> animation,
      Animation<double> secondaryAnimation, Widget child) {
    return FadeTransition(
      opacity: animation,
      child: child,
    );
  }
}
