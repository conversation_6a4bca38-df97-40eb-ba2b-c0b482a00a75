import 'package:play_to_vibe/core/entities/background_opacities_entity.dart';

BackgroundOpacitiesEntity getBackgroundOpacitiesByQuestionTemperature(
    double v) {
  // double newV = v / 50;
  double coldOpacity = v < 40 ? 1 : 0;
  double hotOpacity = v > 60 ? 1 : 0;
  double normalOpacity = coldOpacity == 0 && hotOpacity == 0 ? 1 : 0;

  return BackgroundOpacitiesEntity(
      coldOpacity: coldOpacity,
      hotOpacity: hotOpacity,
      normalOpacity: normalOpacity);
}
