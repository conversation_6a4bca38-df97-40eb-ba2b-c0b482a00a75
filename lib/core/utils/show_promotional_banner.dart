import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:play_to_vibe/analytics.dart';
import 'package:play_to_vibe/app/features/home/<USER>/pages/widgets/promo_slide.dart';
import 'package:play_to_vibe/app/features/home/<USER>/store/promotion_store.dart';
import 'package:play_to_vibe/app/presenter/app_widget.dart';
import 'package:play_to_vibe/app/services/remote_config/remote_config_entity.dart';
import 'package:play_to_vibe/core/utils/quycky_icons_icons.dart';
import 'package:url_launcher/url_launcher.dart';

class ShowPromotionalBanner {
  ShowPromotionalBanner(
      {BannerPromoRuleAndData? banner,
      required String imageUrl,
      required String url}) {
    var promotionStore = Modular.get<PromotionStore>();

    BuildContext context = AppWidget.globalKey.currentState!.context;

    try {
      showGeneralDialog(
        context: context,
        barrierDismissible: true,
        transitionDuration: const Duration(milliseconds: 500),
        barrierLabel: MaterialLocalizations.of(context).dialogLabel,
        barrierColor: Colors.black.withOpacity(0.5),
        pageBuilder: (context, _, __) =>
            banner != null ? getSlideBody(banner) : getBody(imageUrl, url),
        transitionBuilder: getTransitionBuilder,
      ).whenComplete(() => promotionStore.setIsOpenedModal(false));
    } catch (e) {
      print(e);
    }
  }

  void _handleClose() {
    Navigator.pop(AppWidget.globalKey.currentState!.context);
  }

  Widget getBody(String imageUrl, String url) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Container(
          width: 326,
          height: 359,
          constraints: const BoxConstraints(maxWidth: 326, minHeight: 359),
          margin: const EdgeInsets.symmetric(horizontal: 16),
          decoration: BoxDecoration(
              image: DecorationImage(
                  image: NetworkImage(imageUrl), fit: BoxFit.fill),
              color: Colors.white,
              borderRadius: const BorderRadius.all(Radius.circular(30))),
          child: Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  Padding(
                    padding: const EdgeInsets.only(right: 2.0, top: 2),
                    child: IconButton(
                      onPressed: _handleClose,
                      icon: const Icon(QuyckyIcons.close_circle,
                          color: Colors.white),
                    ),
                  )
                ],
              ),
              GestureDetector(
                onTap: () => _launchURL(url),
                child: Container(
                  color: Colors.transparent,
                  height: 300,
                ),
              )
            ],
          ),
        ),
      ],
    );
  }

  Widget getSlideBody(BannerPromoRuleAndData banner) {
    return Column(
      mainAxisSize: MainAxisSize.max,
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        PromoSlide(
          promo: banner,
          onOpenPromo: () => {},
        )
      ],
    );
  }

  Widget getTransitionBuilder(BuildContext context, Animation<double> animation,
      Animation<double> secondaryAnimation, Widget child) {
    Offset begin = const Offset(0, -1);

    return SlideTransition(
      position: CurvedAnimation(
        parent: animation,
        curve: Curves.easeOut,
      ).drive(Tween<Offset>(
        begin: begin,
        end: Offset.zero,
      )),
      child: child,
    );
  }

  _launchURL(String url) async {
    if (await canLaunchUrl(Uri.parse(url))) {
      await launchUrl(Uri.parse(url));
      await Analytics.instance.logEvent(name: 'promo_endgame0_launch');
    } else {
      throw 'Could not launch $url';
    }
  }
}
