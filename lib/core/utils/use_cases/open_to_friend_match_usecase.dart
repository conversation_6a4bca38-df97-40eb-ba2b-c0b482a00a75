import 'dart:async';
import 'dart:core';

import 'package:dartz/dartz.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:play_to_vibe/app/features/friendship/domain/usecases/validate_friendly_invitation_temp_code_usecase.dart';
import 'package:play_to_vibe/core/usecase/errors/failure.dart';
import 'package:play_to_vibe/core/usecase/usecase.dart';
import 'package:play_to_vibe/core/utils/app_routes.dart';
import 'package:play_to_vibe/core/utils/show_message.dart';

class OpenToFriendMatchUsecase implements UseCase<bool, String> {
  final ValidateFriendlyInvitationTempCodeUsecase
      _validateFriendlyInvitationTempCodeUsecase;

  OpenToFriendMatchUsecase(this._validateFriendlyInvitationTempCodeUsecase)
      : super();

  void _handleGoToMatchPage(String friendId) {
    Modular.to.pushReplacementNamed(AppRoutes.checkFriendMatch,
        arguments: {'friendId': friendId, 'isNewFriend': true});
  }

  FutureOr<Null> _handleRequisitionError(dynamic errorResult) {
    final errMessage = errorResult.toString().isEmpty
        ? 'An error occurred, please, verify your internet connection and try again later'
        : errorResult.toString().replaceAll('Exception: ', '');

    ShowMessage(message: errMessage, duration: Duration(seconds: 5));
  }

  Future<bool> _canProceed() async {
    while (true) {
      if (Modular.to.path != AppRoutes.initialRoute) {
        await Future.delayed(const Duration(seconds: 2));
        return true;
      }
      await Future.delayed(const Duration(seconds: 1));
    }
  }

  @override
  Future<Either<Failure, bool>> call(String data) async {
    try {
      if (data.isNotEmpty) {
        await _canProceed();
        final res = await _validateFriendlyInvitationTempCodeUsecase(data);
        res.fold((l) => _handleRequisitionError, (r) {
          ShowMessage(
              message: 'Friend added successfully, opening vibe check...',
              duration: Duration(seconds: 3),
              noButton: true,
              callback: () => _handleGoToMatchPage(r.userId));
        });
      }
    } catch (e) {
      Left(e);
    }
    return const Right(false);
  }
}
