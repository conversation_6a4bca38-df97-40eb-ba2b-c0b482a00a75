import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:play_to_vibe/firebase_options.dart';

class Analytics {
  FirebaseAnalytics? firebaseAnalytics;
  static Analytics? _instance;

  Analytics(this.firebaseAnalytics);

  Future<void> logEvent({
    required String name,
    Map<String, Object>? parameters,
    AnalyticsCallOptions? callOptions,
  }) async {
    _instance!.firebaseAnalytics!
        .logEvent(name: name, parameters: parameters, callOptions: callOptions);
  }

  static void initialize() async {
    FirebaseAnalytics? fbAnalyticsInstance;
    fbAnalyticsInstance = FirebaseAnalytics.instance;

    _instance = Analytics(fbAnalyticsInstance);
  }

  static Analytics get instance {
    return _instance ?? Analytics(null);
  }
}
