import 'package:dartz/dartz.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:play_to_vibe/app/features/terms/domain/repositories/terms_repository.dart';
import 'package:play_to_vibe/app/features/terms/domain/usecases/get_terms_html.dart';
import 'package:play_to_vibe/core/usecase/errors/failure.dart';

class MockTermsRepository extends Mock implements ITermsRepository {}

void main() {
  late ITermsRepository termsRepository;
  late GetTermsHtmlUseCase usecase;

  setUp(() {
    termsRepository = MockTermsRepository();
    usecase = GetTermsHtmlUseCase(termsRepository);
  });

  const termsHtml = '<html><body></body></html>';
  const urlCurrentTerms =
      'https://staging-api.playfultech.io:2053/terms/terms_ecc990f3ab.html';
  test('Should get the html terms and condictions of use page from server',
      () async {
    // Arrange
    when(() => termsRepository.getTermsHtml(urlCurrentTerms))
        .thenAnswer((_) async => const Right<Failure, String>(termsHtml));
    // Act
    final result = await usecase(urlCurrentTerms);
    //Assert
    expect(result, const Right(termsHtml));
    verify(() => termsRepository.getTermsHtml(urlCurrentTerms)).called(1);
  });
  Failure tServerFailure = ServerFailure();
  test('Should return a Failure when don\'t succeed', () async {
    // Arrange
    when(() => termsRepository.getTermsHtml(urlCurrentTerms))
        .thenAnswer((_) async => Left<Failure, String>(tServerFailure));
    // Act
    final result = await usecase(urlCurrentTerms);
    //Assert
    expect(result, Left(tServerFailure));
    verify(() => termsRepository.getTermsHtml(urlCurrentTerms)).called(1);
  });
}
