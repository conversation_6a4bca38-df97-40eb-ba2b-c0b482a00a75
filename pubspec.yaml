name: play_to_vibe
description: Play to Vibe Game App

# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: "none" # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
version: 1.2.3+14

environment:
  sdk: ">=2.17.6 <3.0.0"

## Slidy <PERSON>ripts
vars:
  clean: flutter clean
  get: flutter pub get
  runner: flutter pub run build_runner
scripts:
  mobx_build: $runner build
  mobx_watch: $clean & $get & $runner watch
  mobx_build_clean: $clean & $get & $runner build --delete-conflicting-outputs
  build: flutter pub run build_runner build
  clean: flutter pub run build_runner clean
  buildRelease: flutter build apk --release
  lint: flutter format ./lib
  gen_icons: flutter pub run flutter_launcher_icons -f flutter_launcher_icons.yaml

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.2
  flutter_modular: ^5.0.3
  mobx: ^2.1.0
  flutter_mobx: ^2.0.6+3
  provider: ^6.0.3
  lottie: ^3.0.0
  firebase_core:
  firebase_analytics:
  google_sign_in: ^6.0.2
  # onesignal_flutter: ^3.4.2
  flutter_launcher_icons: ^0.14.3
  sleek_circular_slider: ^2.0.1
  sign_in_with_apple: ^6.1.1
  http: ^0.13.5
  firebase_core_platform_interface:
  flutter_html: ^3.0.0-alpha.6
  flutter_svg: ^2.0.9
  equatable: ^2.0.5
  dartz: ^0.10.1
  mocktail: ^0.3.0
  flutter_triple: ^3.0.0
  dio: ^5.1.1
  socket_io_client: ^3.0.2
  get_storage: ^2.0.3
  image_picker: ^1.1.2
  modal_progress_hud_nsn: ^0.5.1
  localstorage: ^5.0.0
  shared_preferences: ^2.0.16
  file_picker: ^8.0.5
  rive:
  url_launcher: ^6.1.10
  share_plus: ^10.1.4
  firebase: ^9.0.3
  firebase_auth:
  firebase_remote_config: ^5.0.3
  gap: ^3.0.1
  syncfusion_flutter_charts: ^28.1.39
  skeletonizer: ^1.4.1+1
  graphic: ^2.2.1
  firebase_messaging:
  flutter_dotenv: ^5.1.0
  loading_indicator: ^3.1.1
  loading_animation_widget: ^1.2.1
  # flutter_asa_attribution: ^1.0.0
  color_mesh:
  app_links: ^6.4.0
  flutter_local_notifications: ^18.0.1
  sizer: ^3.0.5
  hero_animation: ^1.1.4
  firebase_database: ^11.3.2
  qr_flutter: ^4.1.0
  facebook_app_events: ^0.19.5
  # qr_code_scanner: ^1.0.1
  qr_code_scanner_plus: ^2.0.10+1
  web_socket_channel: ^3.0.3
  app_badge_plus: ^1.2.3

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0
  triple_test: ^2.0.0
  modular_test: ^2.0.0
  mobx_codegen: ^2.0.7+2
  mockito: ^5.4.2

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec
flutter_icons:
  image_path: "assets/logo/AppIcon.png"
  android: true
  ios: true
# The following section is specific to Flutter packages.
flutter:
  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true
  assets:
    # Files
    - .env
    #SVG Image
    - assets/img/svg/
    - assets/img/svg/iq_icons/
    - assets/img/svg/background/
    - assets/img/svg/overlay/
    - assets/img/svg/intro/
    # PNG image
    - assets/img/png/google_logo.png
    - assets/img/png/circles.png
    - assets/img/png/quycky_welcome_logo_white.png
    - assets/img/png/apple_logo.png
    - assets/img/png/fire.png
    - assets/img/png/ice.png
    - assets/img/png/avatar.png
    - assets/img/png/intro/
    - assets/img/png/profile_test.png
    - assets/img/png/circles_radar.png
    # Animations
    - assets/animations/rive/gift_box.riv
    - assets/animations/circles.json
    - assets/animations/flashing.json
  # To add assets to your application, add an assets section, like this:
  # assets:
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages
  fonts:
    - family: QuyckyIcons
      fonts:
        - asset: assets/fonts/QuyckyIcons.ttf
    - family: Roboto
      fonts:
        - asset: assets/fonts/Roboto-Italic.ttf
          style: italic
        - asset: assets/fonts/Roboto-Thin.ttf
        - asset: assets/fonts/Roboto-ThinItalic.ttf
          style: italic
        - asset: assets/fonts/Roboto-Regular.ttf
        - asset: assets/fonts/Roboto-Light.ttf
        - asset: assets/fonts/Roboto-LightItalic.ttf
          style: italic
        - asset: assets/fonts/Roboto-Medium.ttf
        - asset: assets/fonts/Roboto-MediumItalic.ttf
          style: italic
        - asset: assets/fonts/Roboto-Black.ttf
        - asset: assets/fonts/Roboto-BlackItalic.ttf
          style: italic
        - asset: assets/fonts/Roboto-BoldItalic.ttf
          style: italic
          weight: 700
        - asset: assets/fonts/Roboto-Bold.ttf
          weight: 700
    - family: AlbertSans
      fonts:
        - asset: assets/fonts/AlbertSans-Italic.ttf
          style: italic
        - asset: assets/fonts/AlbertSans-Thin.ttf
        - asset: assets/fonts/AlbertSans-ThinItalic.ttf
          style: italic
        - asset: assets/fonts/AlbertSans-Regular.ttf
        - asset: assets/fonts/AlbertSans-Light.ttf
        - asset: assets/fonts/AlbertSans-LightItalic.ttf
          style: italic
        - asset: assets/fonts/AlbertSans-Medium.ttf
        - asset: assets/fonts/AlbertSans-MediumItalic.ttf
          style: italic
        - asset: assets/fonts/AlbertSans-Black.ttf
        - asset: assets/fonts/AlbertSans-BlackItalic.ttf
          style: italic
        - asset: assets/fonts/AlbertSans-BoldItalic.ttf
          style: italic
          weight: 700
        - asset: assets/fonts/AlbertSans-Bold.ttf
          weight: 700
        - asset: assets/fonts/AlbertSans-ExtraBold.ttf
          weight: 900
    - family: SFProText
      fonts:
        - asset: assets/fonts/FontsFree-Net-SFProText-Bold.ttf
          weight: 700
  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages
